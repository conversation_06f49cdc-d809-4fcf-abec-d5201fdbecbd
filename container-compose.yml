# Apple Containerization Framework Compose File
# Each service gets its own lightweight VM for maximum security

version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Containerfile.frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_LIVEKIT_URL=${LIVEKIT_URL}
      - NEXT_PUBLIC_CONN_DETAILS_ENDPOINT=/api/connection-details
    env_file:
      - .env.local
    networks:
      - livekit-network
    restart: unless-stopped
    # Apple's framework provides dedicated IPs, no port forwarding needed
    security:
      isolation: vm  # Each container in its own VM
    
  agent:
    build:
      context: .
      dockerfile: Containerfile.agent
    environment:
      - LIVEKIT_URL=${LIVEKIT_URL}
      - LIVEKIT_API_KEY=${LIVEKIT_API_KEY}
      - LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
    env_file:
      - agent/.env
    networks:
      - livekit-network
    restart: unless-stopped
    depends_on:
      - frontend
    security:
      isolation: vm  # Hardware-level isolation
    
  # Optional: Redis for session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - livekit-network
    restart: unless-stopped
    security:
      isolation: vm

networks:
  livekit-network:
    driver: bridge
    # Apple's framework provides efficient networking between VMs

volumes:
  agent_data:
    driver: local
