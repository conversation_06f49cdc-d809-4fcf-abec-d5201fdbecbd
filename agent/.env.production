# Agent Production Environment Configuration
# LiveKit Voice Agent - Production

# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-production.livekit.cloud
LIVEKIT_API_KEY=your_production_api_key
LIVEKIT_API_SECRET=your_production_api_secret

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_TTS_MODEL=tts-1-hd
OPENAI_TTS_VOICE=alloy

# Deepgram Configuration
DEEPGRAM_API_KEY=your_deepgram_api_key
DEEPGRAM_MODEL=nova-2

# Agent Configuration
AGENT_NAME=LiveKit Voice Agent Production
AGENT_ENVIRONMENT=production
AGENT_LOG_LEVEL=info
AGENT_ENABLE_METRICS=true
AGENT_MAX_SESSIONS=100
AGENT_SESSION_TIMEOUT=300

# Redis Configuration (for session management)
REDIS_URL=redis://livekit-prod-redis:6379
REDIS_DB=0
REDIS_MAX_CONNECTIONS=50

# Container Configuration
CONTAINER_ENVIRONMENT=production
CONTAINER_SERVICE=agent

# Apple Container Framework
APPLE_CONTAINER_ISOLATION=vm
APPLE_CONTAINER_SECURITY=enhanced
APPLE_CONTAINER_PERFORMANCE_MODE=optimized

# Production Optimizations
AGENT_WORKER_THREADS=4
AGENT_MEMORY_LIMIT=2048
AGENT_CPU_LIMIT=2000
