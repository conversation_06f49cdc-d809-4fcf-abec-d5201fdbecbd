import json
import logging
import re
from dataclasses import dataclass, field
from typing import Annotated, Optional

from dotenv import load_dotenv
from pydantic import Field

from livekit import rtc
from livekit.agents import JobContext, WorkerOptions, cli
from livekit.agents.llm import function_tool
from livekit.agents.voice import Agent, AgentSession, RunContext
from livekit.agents.voice.room_io import RoomInputOptions
from livekit.plugins import deepgram, openai, silero

# from livekit.plugins import noise_cancellation

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('agent.log')
    ]
)

logger = logging.getLogger("form-filler-agent")
logger.setLevel(logging.INFO)

load_dotenv()


@dataclass
class UserData:
    ctx: Optional[JobContext] = None
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    customer_email: Optional[str] = None
    should_submit: bool = False


RunContext_T = RunContext[UserData]


async def send_to_frontend(userdata: UserData):
    metrics = {
        "customer_name": userdata.customer_name,
        "customer_phone": userdata.customer_phone,
        "customer_email": userdata.customer_email,
        "should_submit": userdata.should_submit,
    }
    logger.debug(f"Sending data to frontend: {metrics}")
    payload = json.dumps(metrics).encode("utf-8")
    # Publish via the data channel (lossy for speed here)
    await userdata.ctx.room.local_participant.publish_data(
        payload,
        reliable=False
    )


class FormFiller(Agent):
    def __init__(self) -> None:
        super().__init__(
            instructions=(
                f"You are a friendly form filler assistant for an ice cream party. "
                "Your jobs are to ask for the user's name, phone number, and email. "
                "You will need to use the tools provided to you to fill out the form. "
                "After confirming a response, do not say you are setting the value, just move on to the next question. "
                "If the user provides information that doesn't pass validation, politely ask them to provide it in the correct format. "
                "For phone numbers, accept various formats but ensure they have 10-15 digits. "
                "For emails, ensure they have a valid email format with @ and a domain. "
                "When the user wants to submit the form, always confirm their information first by reading back their name, phone, and email, "
                "then ask if they want to proceed with the submission."
            )
        )
    
    async def on_enter(self):
        # userdata: UserData = self.session.userdata
        logger.info("Agent session started")
        await self.session.say("Hello! I'm here to help you sign up for the ice cream party. What's your name?")

    @function_tool()
    async def update_name(
        self,
        name: Annotated[str, Field(description="The customer's name")],
        context: RunContext_T,
    ) -> str:
        """Called when the user provides their name."""
        # Validate name
        if not name or len(name.strip()) < 2:
            return "Please provide a name with at least 2 characters."
        
        # Check for valid name characters
        if not re.match(r"^[a-zA-Z\s\-']+$", name):
            return "Name can only contain letters, spaces, hyphens, and apostrophes."
        
        userdata = context.userdata
        userdata.customer_name = name.strip()
        logger.info(f"Name updated: {name.strip()}")
        await send_to_frontend(userdata)
        return f"The name is updated to {name}"

    @function_tool()
    async def update_phone(
        self,
        phone: Annotated[str, Field(description="The customer's phone number")],
        context: RunContext_T,
    ) -> str:
        """Called when the user provides their phone number."""
        # Remove all non-numeric characters for validation
        cleaned_phone = re.sub(r'\D', '', phone)
        
        # Check for valid phone number length
        if len(cleaned_phone) < 10 or len(cleaned_phone) > 15:
            return "Please provide a valid phone number with 10-15 digits."
        
        # Format US phone numbers
        formatted_phone = phone
        if len(cleaned_phone) == 10:
            formatted_phone = f"({cleaned_phone[:3]}) {cleaned_phone[3:6]}-{cleaned_phone[6:]}"
        elif len(cleaned_phone) == 11 and cleaned_phone.startswith('1'):
            formatted_phone = f"+1 ({cleaned_phone[1:4]}) {cleaned_phone[4:7]}-{cleaned_phone[7:]}"
        
        userdata = context.userdata
        userdata.customer_phone = formatted_phone
        logger.info(f"Phone updated: {formatted_phone}")
        await send_to_frontend(userdata)
        return f"The phone number is updated to {formatted_phone}"

    @function_tool()
    async def update_email(
        self,
        email: Annotated[str, Field(description="The customer's email")],
        context: RunContext_T,
    ) -> str:
        """Called when the user provides their email."""
        # Validate email format
        email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
        if not re.match(email_pattern, email):
            return "Please provide a valid email address."
        
        userdata = context.userdata
        userdata.customer_email = email.lower().strip()
        logger.info(f"Email updated: {email.lower().strip()}")
        await send_to_frontend(userdata)
        return f"The email is updated to {email}"
    
    @function_tool()
    async def get_name(self, context: RunContext_T) -> str:
        """Called to get the user's stored name."""
        return f"User's name is {context.userdata.customer_name}"
    
    @function_tool()
    async def get_phone(self, context: RunContext_T) -> str:
        """Called to get the user's stored phone number."""
        return f"User's phone number is {context.userdata.customer_phone}"
    
    @function_tool()
    async def get_email(self, context: RunContext_T) -> str:
        """Called to get the user's stored email."""
        return f"User's email is {context.userdata.customer_email}"
    
    @function_tool()
    async def submit_form(self, context: RunContext_T) -> str:
        """Called when the user confirms they want to submit the form after you've read back their information."""
        userdata = context.userdata
        
        # Validate all fields are filled
        missing_fields = []
        if not userdata.customer_name:
            missing_fields.append("name")
        if not userdata.customer_phone:
            missing_fields.append("phone number")
        if not userdata.customer_email:
            missing_fields.append("email")
        
        if missing_fields:
            return f"Please provide your {', '.join(missing_fields)} before submitting."
        
        # Prepare confirmation message
        confirmation = (
            f"Perfect! I'm submitting your registration with the following information:\n"
            f"Name: {userdata.customer_name}\n"
            f"Phone: {userdata.customer_phone}\n"
            f"Email: {userdata.customer_email}\n"
        )
        
        userdata.should_submit = True
        logger.info(f"Form submitted - Name: {userdata.customer_name}, Phone: {userdata.customer_phone}, Email: {userdata.customer_email}")
        await send_to_frontend(userdata)
        return f"Great! I've submitted your registration for the ice cream party. You should receive a confirmation email at {userdata.customer_email} soon."


async def entrypoint(ctx: JobContext):
    logger.info(f"Agent starting - Room: {ctx.room.name}")
    await ctx.connect()
    userdata = UserData(ctx=ctx)
    session = AgentSession[UserData](
        userdata=userdata,
        llm=openai.LLM(model="gpt-4.1", temperature=0.7),
        stt=deepgram.STT(model="nova-3", language="multi"),
        tts=openai.TTS(voice="ash"),
        vad=silero.VAD.load(),
        max_tool_steps=5,
        # to use realtime model, replace the stt, llm, tts and vad with the following
        # llm=openai.realtime.RealtimeModel(voice="alloy"),
    )

    @ctx.room.on("data_received")
    def handle_data(packet: rtc.DataPacket):
        try:
            obj = json.loads(packet.data.decode("utf-8"))
            logger.info(f"received data: {obj}")
            if obj.get("field") == "name":
                userdata.customer_name = obj["value"]
                logger.info(f"updated name: {userdata.customer_name}")
            elif obj.get("field") == "phone":
                userdata.customer_phone = obj["value"]
                logger.info(f"updated phone: {userdata.customer_phone}")
            elif obj.get("field") == "email":
                userdata.customer_email = obj["value"]
                logger.info(f"updated email: {userdata.customer_email}")
            else:
                logger.error(f"unknown field: {obj.get('field')}")
        except Exception as e:
            logger.error(f"failed to parse data packet: {e}", exc_info=True)

    await session.start(
        agent=FormFiller(),
        room=ctx.room,
        room_input_options=RoomInputOptions(
            # noise_cancellation=noise_cancellation.BVC(),
        ),
    )


if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))