2025-06-09 13:23:22,785 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-09 13:23:22,789 - livekit.agents - INFO - starting worker
2025-06-09 13:23:22,791 - livekit.agents - INFO - [1msee tracing information at http://localhost:54702/debug[0m
2025-06-09 13:23:22,792 - livekit.agents - WARNING - failed to connect to livekit, retrying in 0s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:22,793 - livekit.agents - WARNING - failed to connect to livekit, retrying in 2s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:24,795 - livekit.agents - WARNING - failed to connect to livekit, retrying in 4s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:28,882 - livekit.agents - WARNING - failed to connect to livekit, retrying in 6s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:34,917 - livekit.agents - WARNING - failed to connect to livekit, retrying in 8s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:42,971 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:23:52,978 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:02,984 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:12,990 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:22,993 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:32,996 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:42,999 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:24:53,001 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:25:03,006 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:25:13,099 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:25:27,208 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:25:32,477 - livekit.agents - WARNING - Running <Task pending name='worker_conn_task' coro=<Worker._connection_task() running at /Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py:16> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at /Users/<USER>/miniconda3/lib/python3.13/asyncio/tasks.py:820]> took too long: 4.28 seconds
2025-06-09 13:25:42,080 - livekit.agents - ERROR - Error in _connection_task
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 699, in _connection_task
    raise RuntimeError(
        f"failed to connect to livekit after {retry_count} attempts",
    ) from None
RuntimeError: failed to connect to livekit after 16 attempts
2025-06-09 13:25:43,061 - livekit.agents - ERROR - worker failed
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/cli/_run.py", line 88, in _worker_run
    await worker.run()
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 483, in run
    await asyncio.gather(*tasks)
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 699, in _connection_task
    raise RuntimeError(
        f"failed to connect to livekit after {retry_count} attempts",
    ) from None
RuntimeError: failed to connect to livekit after 16 attempts
2025-06-09 13:25:45,463 - livekit.agents - WARNING - Running <Task finished name='agent_runner' coro=<run_worker.<locals>._worker_run() done, defined at /Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/cli/_run.py:86> result=None> took too long: 2.40 seconds
2025-06-09 13:25:45,869 - livekit.agents - INFO - shutting down worker
2025-06-09 13:54:16,808 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-09 13:54:16,809 - livekit.agents - DEV - Watching /Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent
2025-06-09 13:54:17,342 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-09 13:54:17,346 - livekit.agents - INFO - starting worker
2025-06-09 13:54:17,349 - livekit.agents - INFO - [1msee tracing information at http://localhost:57531/debug[0m
2025-06-09 13:54:17,349 - livekit.agents - WARNING - failed to connect to livekit, retrying in 0s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:17,351 - livekit.agents - WARNING - failed to connect to livekit, retrying in 2s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:19,353 - livekit.agents - WARNING - failed to connect to livekit, retrying in 4s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:23,354 - livekit.agents - WARNING - failed to connect to livekit, retrying in 6s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:29,357 - livekit.agents - WARNING - failed to connect to livekit, retrying in 8s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:37,359 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:47,363 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:54:57,367 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:07,372 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:17,376 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:27,381 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:37,385 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:47,388 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:55:57,392 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:56:07,394 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:56:17,397 - livekit.agents - WARNING - failed to connect to livekit, retrying in 10s
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 654, in _connection_task
    ws = await self._http_session.ws_connect(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 1059, in _ws_connect
    resp = await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<11 lines>...
    )
    ^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/aiohttp/client.py", line 635, in _request
    raise err_exc_cls(url)
aiohttp.client_exceptions.InvalidUrlClientError: /agent
2025-06-09 13:56:27,401 - livekit.agents - ERROR - Error in _connection_task
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 699, in _connection_task
    raise RuntimeError(
        f"failed to connect to livekit after {retry_count} attempts",
    ) from None
RuntimeError: failed to connect to livekit after 16 attempts
2025-06-09 13:56:27,402 - livekit.agents - ERROR - worker failed
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/cli/_run.py", line 88, in _worker_run
    await worker.run()
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 483, in run
    await asyncio.gather(*tasks)
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/utils/log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent/venv/lib/python3.13/site-packages/livekit/agents/worker.py", line 699, in _connection_task
    raise RuntimeError(
        f"failed to connect to livekit after {retry_count} attempts",
    ) from None
RuntimeError: failed to connect to livekit after 16 attempts
2025-06-09 13:56:27,404 - livekit.agents - INFO - shutting down worker
2025-06-10 23:24:25,190 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-10 23:24:25,191 - livekit.agents - DEV - Watching /Users/<USER>/Documents/GitHub/livekit-voice-agent-form-filler/agent
2025-06-10 23:24:25,692 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-10 23:24:25,696 - livekit.agents - INFO - starting worker
2025-06-10 23:24:25,698 - livekit.agents - INFO - [1msee tracing information at http://localhost:53498/debug[0m
2025-06-10 23:24:25,804 - livekit.agents - INFO - registered worker
2025-06-10 23:24:25,850 - livekit.agents - INFO - received job request
2025-06-10 23:24:25,875 - livekit.agents - INFO - initializing process
2025-06-10 23:24:26,386 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-10 23:24:26,386 - livekit.agents - INFO - process initialized
2025-06-10 23:24:26,386 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-10 23:24:26,387 - form-filler-agent - INFO - Agent starting - Room: voice_assistant_room_3465
2025-06-10 23:24:26,387 - form-filler-agent - INFO - Agent starting - Room: voice_assistant_room_3465
2025-06-10 23:24:26,395 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.25
2025-06-10 23:24:26,395 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.25
2025-06-10 23:24:26,408 - livekit - INFO - livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://alias-ealhha1t.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...
2025-06-10 23:24:26,555 - livekit - DEBUG - rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 155 valid and 0 invalid certs
2025-06-10 23:24:26,555 - livekit - DEBUG - tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 155/155 native root certificates (ignored 0)
2025-06-10 23:24:26,555 - livekit - DEBUG - rustls::client::hs:73:rustls::client::hs - No cached session for DnsName("alias-ealhha1t.livekit.cloud")
2025-06-10 23:24:26,555 - livekit - DEBUG - rustls::client::hs:132:rustls::client::hs - Not resuming any session
2025-06-10 23:24:26,579 - livekit - DEBUG - rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256
2025-06-10 23:24:26,579 - livekit - DEBUG - rustls::client::tls13:142:rustls::client::tls13 - Not resuming
2025-06-10 23:24:26,579 - livekit - DEBUG - rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []
2025-06-10 23:24:26,580 - livekit - DEBUG - rustls::client::hs:472:rustls::client::hs - ALPN protocol is None
2025-06-10 23:24:26,666 - livekit - DEBUG - tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.
2025-06-10 23:24:27,424 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-06-10 23:24:27,425 - form-filler-agent - INFO - Agent session started
2025-06-10 23:24:27,424 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-06-10 23:24:27,425 - form-filler-agent - INFO - Agent session started
2025-06-10 23:24:27,426 - livekit.agents - DEBUG - start reading stream
2025-06-10 23:24:27,426 - livekit.agents - DEBUG - start reading stream
2025-06-10 23:24:27,800 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/audio/speech', 'headers': {'Accept': 'application/octet-stream', 'X-Stainless-Raw-Response': 'stream'}, 'timeout': Timeout(connect=10.0, read=30, write=30, pool=30), 'files': None, 'idempotency_key': 'stainless-python-retry-a426a47f-ed1b-4991-9268-b16f9536d35a', 'json_data': {'input': "Hello! I'm here to help you sign up for the ice cream party.", 'model': 'gpt-4o-mini-tts', 'voice': 'ash', 'response_format': 'mp3', 'speed': 1.0}}
2025-06-10 23:24:27,801 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/audio/speech
2025-06-10 23:24:27,803 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=10.0 socket_options=None
2025-06-10 23:24:27,836 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x1291b3620>
2025-06-10 23:24:27,837 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x119c3c830> server_hostname='api.openai.com' timeout=10.0
2025-06-10 23:24:27,849 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x1291b9090>
2025-06-10 23:24:27,849 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:24:27,850 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:24:27,850 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:24:27,850 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:24:27,850 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:24:29,505 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:24:29 GMT'), (b'Content-Type', b'audio/mpeg'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'608'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'611'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'10000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'9999984'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'0s'), (b'x-request-id', b'req_10259d6ca22518fe3f14ecd7316a83ad'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=VyW2P5bjTXnpVjFXOgkgb0Jso.o_3zH88IBLtE1ykNU-1749561869-*******-ilkaLMlAyKDP_6fPQp..nyd1PvpBjeSA1gMqGw3HipWv2qfHm_6bVzgsWNABeqRPXYB8juUtrOmDr66Am3KJkgShWOuvC4wSc5OaJw40isQ; path=/; expires=Tue, 10-Jun-25 13:54:29 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=jm.1r3akn5VlKHwnpzOe40Is86pjL5sNtWJifnE2u6M-1749561869435-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d9306a2a374ff7-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:24:29,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-10 23:24:29,507 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/audio/speech "200 OK" Headers([('date', 'Tue, 10 Jun 2025 13:24:29 GMT'), ('content-type', 'audio/mpeg'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('access-control-expose-headers', 'X-Request-ID'), ('openai-organization', 'user-ydeonueravmhcjhbtqpemrot'), ('openai-processing-ms', '608'), ('openai-version', '2020-10-01'), ('x-envoy-upstream-service-time', '611'), ('x-ratelimit-limit-requests', '10000'), ('x-ratelimit-limit-tokens', '10000000'), ('x-ratelimit-remaining-requests', '9999'), ('x-ratelimit-remaining-tokens', '9999984'), ('x-ratelimit-reset-requests', '6ms'), ('x-ratelimit-reset-tokens', '0s'), ('x-request-id', 'req_10259d6ca22518fe3f14ecd7316a83ad'), ('strict-transport-security', 'max-age=31536000; includeSubDomains; preload'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=VyW2P5bjTXnpVjFXOgkgb0Jso.o_3zH88IBLtE1ykNU-1749561869-*******-ilkaLMlAyKDP_6fPQp..nyd1PvpBjeSA1gMqGw3HipWv2qfHm_6bVzgsWNABeqRPXYB8juUtrOmDr66Am3KJkgShWOuvC4wSc5OaJw40isQ; path=/; expires=Tue, 10-Jun-25 13:54:29 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('x-content-type-options', 'nosniff'), ('set-cookie', '_cfuvid=jm.1r3akn5VlKHwnpzOe40Is86pjL5sNtWJifnE2u6M-1749561869435-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '94d9306a2a374ff7-MEL'), ('alt-svc', 'h3=":443"; ma=86400')])
2025-06-10 23:24:29,507 - openai._base_client - DEBUG - request_id: req_10259d6ca22518fe3f14ecd7316a83ad
2025-06-10 23:24:29,508 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:24:29,978 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:24:29,978 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:24:29,979 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:24:30,017 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/audio/speech', 'headers': {'Accept': 'application/octet-stream', 'X-Stainless-Raw-Response': 'stream'}, 'timeout': Timeout(connect=10.0, read=30, write=30, pool=30), 'files': None, 'idempotency_key': 'stainless-python-retry-b3d6a9e5-35e9-44f0-828c-ea7dc82fb76a', 'json_data': {'input': "What's your name?", 'model': 'gpt-4o-mini-tts', 'voice': 'ash', 'response_format': 'mp3', 'speed': 1.0}}
2025-06-10 23:24:30,018 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/audio/speech
2025-06-10 23:24:30,019 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:24:30,019 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:24:30,020 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:24:30,020 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:24:30,020 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:24:31,370 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:24:31 GMT'), (b'Content-Type', b'audio/mpeg'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'595'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'606'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'10000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'9999996'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'0s'), (b'x-request-id', b'req_a8cee9522b180d6be5161fc630055354'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d93077bc864ff7-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:24:31,371 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-10 23:24:31,371 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/audio/speech "200 OK" Headers({'date': 'Tue, 10 Jun 2025 13:24:31 GMT', 'content-type': 'audio/mpeg', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-ydeonueravmhcjhbtqpemrot', 'openai-processing-ms': '595', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '606', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '10000000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '9999996', 'x-ratelimit-reset-requests': '6ms', 'x-ratelimit-reset-tokens': '0s', 'x-request-id': 'req_a8cee9522b180d6be5161fc630055354', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '94d93077bc864ff7-MEL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-10 23:24:31,371 - openai._base_client - DEBUG - request_id: req_a8cee9522b180d6be5161fc630055354
2025-06-10 23:24:31,371 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:24:31,538 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:24:31,538 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:24:31,538 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:24:43,337 - livekit.agents - DEBUG - received user transcript
2025-06-10 23:24:43,337 - livekit.agents - DEBUG - received user transcript
2025-06-10 23:24:43,401 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-666485a2-60f8-4768-8795-6cac9cd7df4b', 'json_data': {'messages': [{'role': 'system', 'content': "You are a friendly form filler assistant for an ice cream party. Your jobs are to ask for the user's name, phone number, and email. You will need to use the tools provided to you to fill out the form. After confirming a response, do not say you are setting the value, just move on to the next question. If the user provides information that doesn't pass validation, politely ask them to provide it in the correct format. For phone numbers, accept various formats but ensure they have 10-15 digits. For emails, ensure they have a valid email format with @ and a domain. When the user wants to submit the form, always confirm their information first by reading back their name, phone, and email, then ask if they want to proceed with the submission."}, {'role': 'assistant', 'content': "Hello! I'm here to help you sign up for the ice cream party. What's your name?"}, {'role': 'user', 'content': 'Dane.'}], 'model': 'gpt-4.1', 'stream': True, 'stream_options': {'include_usage': True}, 'temperature': 0.7, 'tools': [{'type': 'function', 'function': {'name': 'get_email', 'strict': True, 'description': "Called to get the user's stored email.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'get_name', 'strict': True, 'description': "Called to get the user's stored name.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'get_phone', 'strict': True, 'description': "Called to get the user's stored phone number.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'submit_form', 'strict': True, 'description': "Called when the user confirms they want to submit the form after you've read back their information.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'update_email', 'strict': True, 'description': 'Called when the user provides their email.', 'parameters': {'properties': {'email': {'description': "The customer's email", 'type': 'string'}}, 'required': ['email'], 'type': 'object', 'additionalProperties': False}}}, {'type': 'function', 'function': {'name': 'update_name', 'strict': True, 'description': 'Called when the user provides their name.', 'parameters': {'properties': {'name': {'description': "The customer's name", 'type': 'string'}}, 'required': ['name'], 'type': 'object', 'additionalProperties': False}}}, {'type': 'function', 'function': {'name': 'update_phone', 'strict': True, 'description': 'Called when the user provides their phone number.', 'parameters': {'properties': {'phone': {'description': "The customer's phone number", 'type': 'string'}}, 'required': ['phone'], 'type': 'object', 'additionalProperties': False}}}]}}
2025-06-10 23:24:43,402 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
2025-06-10 23:24:43,402 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=15.0 socket_options=None
2025-06-10 23:24:43,410 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x1295907d0>
2025-06-10 23:24:43,410 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x119c3c440> server_hostname='api.openai.com' timeout=15.0
2025-06-10 23:24:43,421 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x12920d0f0>
2025-06-10 23:24:43,421 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:24:43,422 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:24:43,422 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:24:43,422 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:24:43,422 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:24:46,235 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:24:46 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'2224'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'2227'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'2000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'1999787'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_2e29121ab993a75d27171685a3848eab'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=aBNYCv2VQCn5.nPwnHALMmAqZO4RPFNxBkeMde4P78o-1749561886-*******-GM73WAe4m..GokJPwQywnE64yHe_dZcSvYQzF8KeZFY9K7MJ3TUOSoJtK2lC.0zty9sm8AluapqHChKdIwuQp9ojklzR_xDLloKcp2lbLag; path=/; expires=Tue, 10-Jun-25 13:54:46 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=xmW5mO1EBkLBG_YJqARbYNsm8HRa4q08nySGGDJVYBE-1749561886239-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d930cb7d2d6b92-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:24:46,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-10 23:24:46,236 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers([('date', 'Tue, 10 Jun 2025 13:24:46 GMT'), ('content-type', 'text/event-stream; charset=utf-8'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('access-control-expose-headers', 'X-Request-ID'), ('openai-organization', 'user-ydeonueravmhcjhbtqpemrot'), ('openai-processing-ms', '2224'), ('openai-version', '2020-10-01'), ('x-envoy-upstream-service-time', '2227'), ('x-ratelimit-limit-requests', '10000'), ('x-ratelimit-limit-tokens', '2000000'), ('x-ratelimit-remaining-requests', '9999'), ('x-ratelimit-remaining-tokens', '1999787'), ('x-ratelimit-reset-requests', '6ms'), ('x-ratelimit-reset-tokens', '6ms'), ('x-request-id', 'req_2e29121ab993a75d27171685a3848eab'), ('strict-transport-security', 'max-age=31536000; includeSubDomains; preload'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=aBNYCv2VQCn5.nPwnHALMmAqZO4RPFNxBkeMde4P78o-1749561886-*******-GM73WAe4m..GokJPwQywnE64yHe_dZcSvYQzF8KeZFY9K7MJ3TUOSoJtK2lC.0zty9sm8AluapqHChKdIwuQp9ojklzR_xDLloKcp2lbLag; path=/; expires=Tue, 10-Jun-25 13:54:46 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('x-content-type-options', 'nosniff'), ('set-cookie', '_cfuvid=xmW5mO1EBkLBG_YJqARbYNsm8HRa4q08nySGGDJVYBE-1749561886239-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '94d930cb7d2d6b92-MEL'), ('alt-svc', 'h3=":443"; ma=86400')])
2025-06-10 23:24:46,237 - openai._base_client - DEBUG - request_id: req_2e29121ab993a75d27171685a3848eab
2025-06-10 23:24:46,237 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:24:46,886 - livekit.agents - DEBUG - executing tool
2025-06-10 23:24:46,886 - livekit.agents - DEBUG - executing tool
2025-06-10 23:24:46,887 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:24:46,887 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:24:46,887 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:24:46,888 - livekit.agents - DEBUG - tools execution completed
2025-06-10 23:24:46,888 - livekit.agents - DEBUG - tools execution completed
2025-06-10 23:24:46,895 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-34848c14-3331-4b01-a2ae-be3621216715', 'json_data': {'messages': [{'role': 'system', 'content': "You are a friendly form filler assistant for an ice cream party. Your jobs are to ask for the user's name, phone number, and email. You will need to use the tools provided to you to fill out the form. After confirming a response, do not say you are setting the value, just move on to the next question. If the user provides information that doesn't pass validation, politely ask them to provide it in the correct format. For phone numbers, accept various formats but ensure they have 10-15 digits. For emails, ensure they have a valid email format with @ and a domain. When the user wants to submit the form, always confirm their information first by reading back their name, phone, and email, then ask if they want to proceed with the submission."}, {'role': 'assistant', 'content': "Hello! I'm here to help you sign up for the ice cream party. What's your name?"}, {'role': 'user', 'content': 'Dane.'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_pTM2cCTwf1PLmYdJOPVbsWMd', 'type': 'function', 'function': {'name': 'update_name', 'arguments': '{"name":"Dane."}'}}]}, {'role': 'tool', 'tool_call_id': 'call_pTM2cCTwf1PLmYdJOPVbsWMd', 'content': 'Name can only contain letters, spaces, hyphens, and apostrophes.'}], 'model': 'gpt-4.1', 'stream': True, 'stream_options': {'include_usage': True}, 'temperature': 0.7, 'tool_choice': 'auto', 'tools': [{'type': 'function', 'function': {'name': 'get_email', 'strict': True, 'description': "Called to get the user's stored email.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'get_name', 'strict': True, 'description': "Called to get the user's stored name.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'get_phone', 'strict': True, 'description': "Called to get the user's stored phone number.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'submit_form', 'strict': True, 'description': "Called when the user confirms they want to submit the form after you've read back their information.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'update_email', 'strict': True, 'description': 'Called when the user provides their email.', 'parameters': {'properties': {'email': {'description': "The customer's email", 'type': 'string'}}, 'required': ['email'], 'type': 'object', 'additionalProperties': False}}}, {'type': 'function', 'function': {'name': 'update_name', 'strict': True, 'description': 'Called when the user provides their name.', 'parameters': {'properties': {'name': {'description': "The customer's name", 'type': 'string'}}, 'required': ['name'], 'type': 'object', 'additionalProperties': False}}}, {'type': 'function', 'function': {'name': 'update_phone', 'strict': True, 'description': 'Called when the user provides their phone number.', 'parameters': {'properties': {'phone': {'description': "The customer's phone number", 'type': 'string'}}, 'required': ['phone'], 'type': 'object', 'additionalProperties': False}}}]}}
2025-06-10 23:24:46,896 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
2025-06-10 23:24:46,897 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:24:46,897 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:24:46,897 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:24:46,898 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:24:46,898 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:24:49,466 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:24:49 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'1936'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'1941'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'2000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'1999770'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_91ddebcf40bb102539e676a23ce14e43'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d930e12ea46b92-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:24:49,466 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-10 23:24:49,466 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers({'date': 'Tue, 10 Jun 2025 13:24:49 GMT', 'content-type': 'text/event-stream; charset=utf-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-ydeonueravmhcjhbtqpemrot', 'openai-processing-ms': '1936', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '1941', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '2000000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '1999770', 'x-ratelimit-reset-requests': '6ms', 'x-ratelimit-reset-tokens': '6ms', 'x-request-id': 'req_91ddebcf40bb102539e676a23ce14e43', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '94d930e12ea46b92-MEL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-10 23:24:49,467 - openai._base_client - DEBUG - request_id: req_91ddebcf40bb102539e676a23ce14e43
2025-06-10 23:24:49,467 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:24:50,790 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/audio/speech', 'headers': {'Accept': 'application/octet-stream', 'X-Stainless-Raw-Response': 'stream'}, 'timeout': Timeout(connect=10.0, read=30, write=30, pool=30), 'files': None, 'idempotency_key': 'stainless-python-retry-1f720a64-bb8b-4829-a431-29939e5ad7bb', 'json_data': {'input': 'It looks like your name contains an invalid character (the period at the end).', 'model': 'gpt-4o-mini-tts', 'voice': 'ash', 'response_format': 'mp3', 'speed': 1.0}}
2025-06-10 23:24:50,791 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/audio/speech
2025-06-10 23:24:50,791 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:24:50,791 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:24:50,791 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:24:50,791 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:24:50,791 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:24:52,069 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:24:52,069 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:24:52,069 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:24:52,212 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:24:52 GMT'), (b'Content-Type', b'audio/mpeg'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'892'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'898'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'10000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'9999980'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'0s'), (b'x-request-id', b'req_e21e3230a46482b7226cbb0323daa8c5'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d930f989904ff7-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:24:52,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-10 23:24:52,213 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/audio/speech "200 OK" Headers({'date': 'Tue, 10 Jun 2025 13:24:52 GMT', 'content-type': 'audio/mpeg', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-ydeonueravmhcjhbtqpemrot', 'openai-processing-ms': '892', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '898', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '10000000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '9999980', 'x-ratelimit-reset-requests': '6ms', 'x-ratelimit-reset-tokens': '0s', 'x-request-id': 'req_e21e3230a46482b7226cbb0323daa8c5', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '94d930f989904ff7-MEL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-10 23:24:52,213 - openai._base_client - DEBUG - request_id: req_e21e3230a46482b7226cbb0323daa8c5
2025-06-10 23:24:52,213 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:24:52,651 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:24:52,651 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:24:52,652 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:24:52,673 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/audio/speech', 'headers': {'Accept': 'application/octet-stream', 'X-Stainless-Raw-Response': 'stream'}, 'timeout': Timeout(connect=10.0, read=30, write=30, pool=30), 'files': None, 'idempotency_key': 'stainless-python-retry-ee65d272-5c47-4dcf-af99-4afa77b873c4', 'json_data': {'input': 'Could you please provide your name using only letters, spaces, hyphens, or apostrophes?', 'model': 'gpt-4o-mini-tts', 'voice': 'ash', 'response_format': 'mp3', 'speed': 1.0}}
2025-06-10 23:24:52,673 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/audio/speech
2025-06-10 23:24:52,673 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:24:52,674 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:24:52,674 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:24:52,675 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:24:52,675 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:24:54,021 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:24:54 GMT'), (b'Content-Type', b'audio/mpeg'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'706'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'712'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'10000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'9999979'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'0s'), (b'x-request-id', b'req_8202a245a8a4c92ae55bcecd195ea023'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d9310548de4ff7-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:24:54,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-10 23:24:54,021 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/audio/speech "200 OK" Headers({'date': 'Tue, 10 Jun 2025 13:24:54 GMT', 'content-type': 'audio/mpeg', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-ydeonueravmhcjhbtqpemrot', 'openai-processing-ms': '706', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '712', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '10000000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '9999979', 'x-ratelimit-reset-requests': '6ms', 'x-ratelimit-reset-tokens': '0s', 'x-request-id': 'req_8202a245a8a4c92ae55bcecd195ea023', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '94d9310548de4ff7-MEL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-10 23:24:54,022 - openai._base_client - DEBUG - request_id: req_8202a245a8a4c92ae55bcecd195ea023
2025-06-10 23:24:54,022 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:24:54,726 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:24:54,726 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:24:54,726 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:46:12,192 - livekit.agents - DEBUG - received user transcript
2025-06-10 23:46:12,192 - livekit.agents - DEBUG - received user transcript
2025-06-10 23:46:12,208 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-13fa8daa-ee6f-4488-aad3-4a7904ec713d', 'json_data': {'messages': [{'role': 'system', 'content': "You are a friendly form filler assistant for an ice cream party. Your jobs are to ask for the user's name, phone number, and email. You will need to use the tools provided to you to fill out the form. After confirming a response, do not say you are setting the value, just move on to the next question. If the user provides information that doesn't pass validation, politely ask them to provide it in the correct format. For phone numbers, accept various formats but ensure they have 10-15 digits. For emails, ensure they have a valid email format with @ and a domain. When the user wants to submit the form, always confirm their information first by reading back their name, phone, and email, then ask if they want to proceed with the submission."}, {'role': 'assistant', 'content': "Hello! I'm here to help you sign up for the ice cream party. What's your name?"}, {'role': 'user', 'content': 'Dane.'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_pTM2cCTwf1PLmYdJOPVbsWMd', 'type': 'function', 'function': {'name': 'update_name', 'arguments': '{"name":"Dane."}'}}]}, {'role': 'tool', 'tool_call_id': 'call_pTM2cCTwf1PLmYdJOPVbsWMd', 'content': 'Name can only contain letters, spaces, hyphens, and apostrophes.'}, {'role': 'assistant', 'content': 'It looks like your name contains an invalid character (the period at the end). Could you please provide your name using only letters, spaces, hyphens, or apostrophes?'}, {'role': 'user', 'content': 'Can'}], 'model': 'gpt-4.1', 'stream': True, 'stream_options': {'include_usage': True}, 'temperature': 0.7, 'tools': [{'type': 'function', 'function': {'name': 'get_email', 'strict': True, 'description': "Called to get the user's stored email.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'get_name', 'strict': True, 'description': "Called to get the user's stored name.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'get_phone', 'strict': True, 'description': "Called to get the user's stored phone number.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'submit_form', 'strict': True, 'description': "Called when the user confirms they want to submit the form after you've read back their information.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'update_email', 'strict': True, 'description': 'Called when the user provides their email.', 'parameters': {'properties': {'email': {'description': "The customer's email", 'type': 'string'}}, 'required': ['email'], 'type': 'object', 'additionalProperties': False}}}, {'type': 'function', 'function': {'name': 'update_name', 'strict': True, 'description': 'Called when the user provides their name.', 'parameters': {'properties': {'name': {'description': "The customer's name", 'type': 'string'}}, 'required': ['name'], 'type': 'object', 'additionalProperties': False}}}, {'type': 'function', 'function': {'name': 'update_phone', 'strict': True, 'description': 'Called when the user provides their phone number.', 'parameters': {'properties': {'phone': {'description': "The customer's phone number", 'type': 'string'}}, 'required': ['phone'], 'type': 'object', 'additionalProperties': False}}}]}}
2025-06-10 23:46:12,209 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
2025-06-10 23:46:12,210 - httpcore.connection - DEBUG - close.started
2025-06-10 23:46:12,211 - httpcore.connection - DEBUG - close.complete
2025-06-10 23:46:12,211 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=15.0 socket_options=None
2025-06-10 23:46:12,247 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x12920d810>
2025-06-10 23:46:12,247 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x119c3c440> server_hostname='api.openai.com' timeout=15.0
2025-06-10 23:46:12,272 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x12954d010>
2025-06-10 23:46:12,272 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:46:12,273 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:46:12,273 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:46:12,273 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:46:12,273 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:46:17,047 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:46:17 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'3411'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'3421'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'2000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'1999726'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'8ms'), (b'x-request-id', b'req_2bd3fc51aff6fd1775a0d0924b642254'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=aef61X5_Z_8DkrtvOn2lrl3QKfVS33xqF9ROp2nbjU8-1749563177-*******-1VYat5wPh0mHPAhVQ34wQjBYIwqWjO4g4E.26PebL4953CGY7z6_cJVHLhumQJGQPeOZZC6KBnLnzw03dFNvnijybILJFeXDMq3spg8H.ps; path=/; expires=Tue, 10-Jun-25 14:16:17 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d95042cf4bf0dd-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:46:17,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-10 23:46:17,047 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers({'date': 'Tue, 10 Jun 2025 13:46:17 GMT', 'content-type': 'text/event-stream; charset=utf-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-ydeonueravmhcjhbtqpemrot', 'openai-processing-ms': '3411', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '3421', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '2000000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '1999726', 'x-ratelimit-reset-requests': '6ms', 'x-ratelimit-reset-tokens': '8ms', 'x-request-id': 'req_2bd3fc51aff6fd1775a0d0924b642254', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=aef61X5_Z_8DkrtvOn2lrl3QKfVS33xqF9ROp2nbjU8-1749563177-*******-1VYat5wPh0mHPAhVQ34wQjBYIwqWjO4g4E.26PebL4953CGY7z6_cJVHLhumQJGQPeOZZC6KBnLnzw03dFNvnijybILJFeXDMq3spg8H.ps; path=/; expires=Tue, 10-Jun-25 14:16:17 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '94d95042cf4bf0dd-MEL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-10 23:46:17,048 - openai._base_client - DEBUG - request_id: req_2bd3fc51aff6fd1775a0d0924b642254
2025-06-10 23:46:17,048 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:46:17,362 - livekit.agents - DEBUG - executing tool
2025-06-10 23:46:17,362 - form-filler-agent - INFO - Name updated: Can
2025-06-10 23:46:17,362 - livekit.agents - DEBUG - executing tool
2025-06-10 23:46:17,362 - form-filler-agent - INFO - Name updated: Can
2025-06-10 23:46:17,367 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:46:17,367 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:46:17,367 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:46:17,368 - livekit.agents - DEBUG - tools execution completed
2025-06-10 23:46:17,368 - livekit.agents - DEBUG - tools execution completed
2025-06-10 23:46:17,372 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-78211617-4b2c-4220-94a5-1389a2331e2b', 'json_data': {'messages': [{'role': 'system', 'content': "You are a friendly form filler assistant for an ice cream party. Your jobs are to ask for the user's name, phone number, and email. You will need to use the tools provided to you to fill out the form. After confirming a response, do not say you are setting the value, just move on to the next question. If the user provides information that doesn't pass validation, politely ask them to provide it in the correct format. For phone numbers, accept various formats but ensure they have 10-15 digits. For emails, ensure they have a valid email format with @ and a domain. When the user wants to submit the form, always confirm their information first by reading back their name, phone, and email, then ask if they want to proceed with the submission."}, {'role': 'assistant', 'content': "Hello! I'm here to help you sign up for the ice cream party. What's your name?"}, {'role': 'user', 'content': 'Dane.'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_pTM2cCTwf1PLmYdJOPVbsWMd', 'type': 'function', 'function': {'name': 'update_name', 'arguments': '{"name":"Dane."}'}}]}, {'role': 'tool', 'tool_call_id': 'call_pTM2cCTwf1PLmYdJOPVbsWMd', 'content': 'Name can only contain letters, spaces, hyphens, and apostrophes.'}, {'role': 'assistant', 'content': 'It looks like your name contains an invalid character (the period at the end). Could you please provide your name using only letters, spaces, hyphens, or apostrophes?'}, {'role': 'user', 'content': 'Can'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_uBKHrpOW2tnjFQVrTV4BgGAR', 'type': 'function', 'function': {'name': 'update_name', 'arguments': '{"name":"Can"}'}}]}, {'role': 'tool', 'tool_call_id': 'call_uBKHrpOW2tnjFQVrTV4BgGAR', 'content': 'The name is updated to Can'}], 'model': 'gpt-4.1', 'stream': True, 'stream_options': {'include_usage': True}, 'temperature': 0.7, 'tool_choice': 'auto', 'tools': [{'type': 'function', 'function': {'name': 'get_email', 'strict': True, 'description': "Called to get the user's stored email.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'get_name', 'strict': True, 'description': "Called to get the user's stored name.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'get_phone', 'strict': True, 'description': "Called to get the user's stored phone number.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'submit_form', 'strict': True, 'description': "Called when the user confirms they want to submit the form after you've read back their information.", 'parameters': {'properties': {}, 'type': 'object', 'additionalProperties': False, 'required': []}}}, {'type': 'function', 'function': {'name': 'update_email', 'strict': True, 'description': 'Called when the user provides their email.', 'parameters': {'properties': {'email': {'description': "The customer's email", 'type': 'string'}}, 'required': ['email'], 'type': 'object', 'additionalProperties': False}}}, {'type': 'function', 'function': {'name': 'update_name', 'strict': True, 'description': 'Called when the user provides their name.', 'parameters': {'properties': {'name': {'description': "The customer's name", 'type': 'string'}}, 'required': ['name'], 'type': 'object', 'additionalProperties': False}}}, {'type': 'function', 'function': {'name': 'update_phone', 'strict': True, 'description': 'Called when the user provides their phone number.', 'parameters': {'properties': {'phone': {'description': "The customer's phone number", 'type': 'string'}}, 'required': ['phone'], 'type': 'object', 'additionalProperties': False}}}]}}
2025-06-10 23:46:17,373 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
2025-06-10 23:46:17,373 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:46:17,373 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:46:17,373 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:46:17,374 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:46:17,374 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:46:20,387 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:46:20 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'2161'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'2171'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'2000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'1999717'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'8ms'), (b'x-request-id', b'req_77574222dad25c7e8d12041fd5d3036f'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d95062ab8bf0dd-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:46:20,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-10 23:46:20,388 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers({'date': 'Tue, 10 Jun 2025 13:46:20 GMT', 'content-type': 'text/event-stream; charset=utf-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-ydeonueravmhcjhbtqpemrot', 'openai-processing-ms': '2161', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '2171', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '2000000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '1999717', 'x-ratelimit-reset-requests': '6ms', 'x-ratelimit-reset-tokens': '8ms', 'x-request-id': 'req_77574222dad25c7e8d12041fd5d3036f', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '94d95062ab8bf0dd-MEL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-10 23:46:20,389 - openai._base_client - DEBUG - request_id: req_77574222dad25c7e8d12041fd5d3036f
2025-06-10 23:46:20,389 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:46:21,311 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/audio/speech', 'headers': {'Accept': 'application/octet-stream', 'X-Stainless-Raw-Response': 'stream'}, 'timeout': Timeout(connect=10.0, read=30, write=30, pool=30), 'files': None, 'idempotency_key': 'stainless-python-retry-c27d7047-cd2e-44b3-a456-1ffd1d9b79df', 'json_data': {'input': 'Thank you, Can! Could you please provide your phone number?', 'model': 'gpt-4o-mini-tts', 'voice': 'ash', 'response_format': 'mp3', 'speed': 1.0}}
2025-06-10 23:46:21,312 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/audio/speech
2025-06-10 23:46:21,312 - httpcore.connection - DEBUG - close.started
2025-06-10 23:46:21,312 - httpcore.connection - DEBUG - close.complete
2025-06-10 23:46:21,312 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=10.0 socket_options=None
2025-06-10 23:46:21,327 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x11cc9ff00>
2025-06-10 23:46:21,327 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x119c3c830> server_hostname='api.openai.com' timeout=10.0
2025-06-10 23:46:21,338 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x11cc9d480>
2025-06-10 23:46:21,338 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:46:21,339 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:46:21,339 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:46:21,339 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:46:21,339 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:46:22,329 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:46:22,330 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:46:22,330 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:46:22,592 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:46:22 GMT'), (b'Content-Type', b'audio/mpeg'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'726'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'728'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'10000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'9999986'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'0s'), (b'x-request-id', b'req_4af8ab809c88769b0ca05aeb564f68a4'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=a087P9xuf.zVUzm5SLCdJftYGeh6PB7bmm9L6UwkxkY-1749563182-*******-_QlM4cBWx.u3qBOk73pQv_KSm1_JD3UHst7ivfFfdSXtpQlTsUDGGWHFZBWrAnq4_xSm68zGHFY6LWyQmeDcFGA10cuyQZICa5nBDmvWzP0; path=/; expires=Tue, 10-Jun-25 14:16:22 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d9507b782a5a67-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:46:22,592 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-10 23:46:22,592 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/audio/speech "200 OK" Headers({'date': 'Tue, 10 Jun 2025 13:46:22 GMT', 'content-type': 'audio/mpeg', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-ydeonueravmhcjhbtqpemrot', 'openai-processing-ms': '726', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '728', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '10000000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '9999986', 'x-ratelimit-reset-requests': '6ms', 'x-ratelimit-reset-tokens': '0s', 'x-request-id': 'req_4af8ab809c88769b0ca05aeb564f68a4', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=a087P9xuf.zVUzm5SLCdJftYGeh6PB7bmm9L6UwkxkY-1749563182-*******-_QlM4cBWx.u3qBOk73pQv_KSm1_JD3UHst7ivfFfdSXtpQlTsUDGGWHFZBWrAnq4_xSm68zGHFY6LWyQmeDcFGA10cuyQZICa5nBDmvWzP0; path=/; expires=Tue, 10-Jun-25 14:16:22 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '94d9507b782a5a67-MEL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-10 23:46:22,593 - openai._base_client - DEBUG - request_id: req_4af8ab809c88769b0ca05aeb564f68a4
2025-06-10 23:46:22,593 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:46:22,963 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:46:22,963 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:46:22,963 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:46:22,989 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/audio/speech', 'headers': {'Accept': 'application/octet-stream', 'X-Stainless-Raw-Response': 'stream'}, 'timeout': Timeout(connect=10.0, read=30, write=30, pool=30), 'files': None, 'idempotency_key': 'stainless-python-retry-b4c18882-5ca2-4a69-ab6a-9ac4bd26db3f', 'json_data': {'input': 'It should have 10 to 15 digits, but you can include spaces, dashes, or parentheses if you prefer.', 'model': 'gpt-4o-mini-tts', 'voice': 'ash', 'response_format': 'mp3', 'speed': 1.0}}
2025-06-10 23:46:22,990 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/audio/speech
2025-06-10 23:46:22,990 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-10 23:46:22,991 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-10 23:46:22,991 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-10 23:46:22,991 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-10 23:46:22,991 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-10 23:46:24,231 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Tue, 10 Jun 2025 13:46:24 GMT'), (b'Content-Type', b'audio/mpeg'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-ydeonueravmhcjhbtqpemrot'), (b'openai-processing-ms', b'571'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'575'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'10000000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'9999975'), (b'x-ratelimit-reset-requests', b'6ms'), (b'x-ratelimit-reset-tokens', b'0s'), (b'x-request-id', b'req_3fe5e5f781b96a2df4e9a897a32e5492'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'94d95085cf315a67-MEL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-10 23:46:24,231 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2025-06-10 23:46:24,231 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/audio/speech "200 OK" Headers({'date': 'Tue, 10 Jun 2025 13:46:24 GMT', 'content-type': 'audio/mpeg', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-ydeonueravmhcjhbtqpemrot', 'openai-processing-ms': '571', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '575', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '10000000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '9999975', 'x-ratelimit-reset-requests': '6ms', 'x-ratelimit-reset-tokens': '0s', 'x-request-id': 'req_3fe5e5f781b96a2df4e9a897a32e5492', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '94d95085cf315a67-MEL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-10 23:46:24,232 - openai._base_client - DEBUG - request_id: req_3fe5e5f781b96a2df4e9a897a32e5492
2025-06-10 23:46:24,232 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-10 23:46:24,976 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-10 23:46:24,976 - httpcore.http11 - DEBUG - response_closed.started
2025-06-10 23:46:24,976 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-10 23:46:41,229 - livekit.agents - DEBUG - stream closed
2025-06-10 23:46:41,229 - livekit.agents - DEBUG - stream closed
2025-06-10 23:47:02,418 - livekit - DEBUG - tungstenite::protocol:666:tungstenite::protocol - Received close frame: Some(CloseFrame { code: Normal, reason: "" })
2025-06-10 23:47:02,420 - livekit.agents - DEBUG - shutting down job task
2025-06-10 23:47:02,421 - livekit - DEBUG - tungstenite::protocol:683:tungstenite::protocol - Replying to close with Frame { header: FrameHeader { is_final: true, rsv1: false, rsv2: false, rsv3: false, opcode: Control(Close), mask: None }, payload: [3, 232] }
2025-06-10 23:47:02,421 - livekit.agents - INFO - process exiting
2025-06-10 23:47:02,420 - livekit.agents - DEBUG - shutting down job task
2025-06-10 23:47:02,422 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-06-10 23:47:02,422 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-06-10 23:47:02,425 - livekit - WARNING - livekit::rtc_engine:450:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
2025-06-10 23:47:02,425 - livekit - WARNING - livekit::rtc_engine:450:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
