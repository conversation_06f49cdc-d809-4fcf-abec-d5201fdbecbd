import unittest
from unittest.mock import AsyncMock, MagicMock, patch
import re
from dataclasses import dataclass
from agent import FormFiller, UserData, send_to_frontend


class TestFormFiller(unittest.TestCase):
    def setUp(self):
        self.agent = FormFiller()
        self.mock_ctx = MagicMock()
        self.mock_ctx.room.local_participant.publish_data = AsyncMock()
        self.userdata = UserData(ctx=self.mock_ctx)
        self.mock_context = MagicMock()
        self.mock_context.userdata = self.userdata

    async def test_update_name_valid(self):
        """Test updating name with valid input"""
        result = await self.agent.update_name("<PERSON>", self.mock_context)
        self.assertEqual(self.userdata.customer_name, "<PERSON>")
        self.assertIn("updated to <PERSON>", result)
        
    async def test_update_name_invalid_short(self):
        """Test updating name with too short input"""
        result = await self.agent.update_name("J", self.mock_context)
        self.assertIsNone(self.userdata.customer_name)
        self.assertIn("at least 2 characters", result)
        
    async def test_update_name_invalid_characters(self):
        """Test updating name with invalid characters"""
        result = await self.agent.update_name("John123", self.mock_context)
        self.assertIsNone(self.userdata.customer_name)
        self.assertIn("only contain letters", result)
        
    async def test_update_phone_valid_10_digits(self):
        """Test updating phone with valid 10-digit US number"""
        result = await self.agent.update_phone("1234567890", self.mock_context)
        self.assertEqual(self.userdata.customer_phone, "(*************")
        self.assertIn("updated to (*************", result)
        
    async def test_update_phone_valid_11_digits(self):
        """Test updating phone with valid 11-digit US number"""
        result = await self.agent.update_phone("11234567890", self.mock_context)
        self.assertEqual(self.userdata.customer_phone, "+1 (*************")
        self.assertIn("updated to +1 (*************", result)
        
    async def test_update_phone_invalid_length(self):
        """Test updating phone with invalid length"""
        result = await self.agent.update_phone("123", self.mock_context)
        self.assertIsNone(self.userdata.customer_phone)
        self.assertIn("10-15 digits", result)
        
    async def test_update_email_valid(self):
        """Test updating email with valid format"""
        result = await self.agent.update_email("<EMAIL>", self.mock_context)
        self.assertEqual(self.userdata.customer_email, "<EMAIL>")
        self.assertIn("<NAME_EMAIL>", result)
        
    async def test_update_email_invalid(self):
        """Test updating email with invalid format"""
        result = await self.agent.update_email("not-an-email", self.mock_context)
        self.assertIsNone(self.userdata.customer_email)
        self.assertIn("valid email address", result)
        
    async def test_get_name(self):
        """Test getting stored name"""
        self.userdata.customer_name = "John Doe"
        result = await self.agent.get_name(self.mock_context)
        self.assertIn("John Doe", result)
        
    async def test_get_phone(self):
        """Test getting stored phone"""
        self.userdata.customer_phone = "(*************"
        result = await self.agent.get_phone(self.mock_context)
        self.assertIn("(*************", result)
        
    async def test_get_email(self):
        """Test getting stored email"""
        self.userdata.customer_email = "<EMAIL>"
        result = await self.agent.get_email(self.mock_context)
        self.assertIn("<EMAIL>", result)
        
    async def test_submit_form_complete(self):
        """Test submitting form with all fields filled"""
        self.userdata.customer_name = "John Doe"
        self.userdata.customer_phone = "(*************"
        self.userdata.customer_email = "<EMAIL>"
        
        result = await self.agent.submit_form(self.mock_context)
        self.assertTrue(self.userdata.should_submit)
        self.assertIn("submitted your registration", result)
        
    async def test_submit_form_missing_fields(self):
        """Test submitting form with missing fields"""
        self.userdata.customer_name = "John Doe"
        # phone and email are None
        
        result = await self.agent.submit_form(self.mock_context)
        self.assertFalse(self.userdata.should_submit)
        self.assertIn("phone number", result)
        self.assertIn("email", result)


class TestSendToFrontend(unittest.TestCase):
    async def test_send_to_frontend(self):
        """Test sending data to frontend"""
        mock_ctx = MagicMock()
        mock_ctx.room.local_participant.publish_data = AsyncMock()
        
        userdata = UserData(
            ctx=mock_ctx,
            customer_name="John Doe",
            customer_phone="(*************",
            customer_email="<EMAIL>",
            should_submit=True
        )
        
        await send_to_frontend(userdata)
        
        # Verify publish_data was called
        mock_ctx.room.local_participant.publish_data.assert_called_once()
        
        # Get the published data
        call_args = mock_ctx.room.local_participant.publish_data.call_args
        published_data = call_args[0][0].decode('utf-8')
        
        # Verify the data structure
        import json
        data = json.loads(published_data)
        self.assertEqual(data['customer_name'], "John Doe")
        self.assertEqual(data['customer_phone'], "(*************")
        self.assertEqual(data['customer_email'], "<EMAIL>")
        self.assertTrue(data['should_submit'])


if __name__ == '__main__':
    # Run async tests
    import asyncio
    
    async def run_async_tests():
        # Create test suite
        suite = unittest.TestLoader().loadTestsFromTestCase(TestFormFiller)
        suite.addTests(unittest.TestLoader().loadTestsFromTestCase(TestSendToFrontend))
        
        # Run each test
        for test in suite:
            if asyncio.iscoroutinefunction(test._testMethodName):
                await getattr(test, test._testMethodName)()
            else:
                test.debug()
    
    # Run the tests
    asyncio.run(run_async_tests())