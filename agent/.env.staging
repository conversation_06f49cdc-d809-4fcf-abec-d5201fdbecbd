# Agent Staging Environment Configuration
# LiveKit Voice Agent - Staging

# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-staging.livekit.cloud
LIVEKIT_API_KEY=your_staging_api_key
LIVEKIT_API_SECRET=your_staging_api_secret

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_TTS_MODEL=tts-1
OPENAI_TTS_VOICE=alloy

# Deepgram Configuration
DEEPGRAM_API_KEY=your_deepgram_api_key
DEEPGRAM_MODEL=nova-2

# Agent Configuration
AGENT_NAME=LiveKit Voice Agent Staging
AGENT_ENVIRONMENT=staging
AGENT_LOG_LEVEL=debug
AGENT_ENABLE_METRICS=true

# Redis Configuration (for session management)
REDIS_URL=redis://livekit-staging-redis:6379
REDIS_DB=0

# Container Configuration
CONTAINER_ENVIRONMENT=staging
CONTAINER_SERVICE=agent

# Apple Container Framework
APPLE_CONTAINER_ISOLATION=vm
APPLE_CONTAINER_SECURITY=enhanced
