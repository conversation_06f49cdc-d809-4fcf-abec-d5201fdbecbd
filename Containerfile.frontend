# Apple Containerization Framework - Frontend Container
# Using OCI-compliant format for Apple's container system

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json bun.lock* ./

# Install dependencies using bun (faster on Apple Silicon)
RUN npm install -g bun && bun install

# Copy source code
COPY . .

# Build the application
RUN bun build

# Expose port
EXPOSE 3000

# Health check for Apple's container monitoring
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application
CMD ["bun", "start"]
