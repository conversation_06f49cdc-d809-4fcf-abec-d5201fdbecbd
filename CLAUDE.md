# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Frontend Development

```bash
# Install dependencies
bun install

# Start development server
bun dev

# Build for production
bun build

# Run production server
bun start

# Lint code
bun lint

# Check code formatting
bun format:check

# Fix code formatting
bun format:write
```

### Agent Development

```bash
cd agent

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Download required model files
python agent.py download-files

# Start agent in development mode
python agent.py dev
```

## Architecture

This project consists of two main components that communicate via LiveKit's real-time infrastructure:

### Frontend (Next.js)

- **Purpose**: Web interface for voice-driven form filling
- **Stack**: Next.js 14 (App Router), React 18, TypeScript, Tailwind CSS
- **Key Components**:
  - `app/page.tsx` - Main form interface with LiveKit integration
  - `components/Form.tsx` - Form component that syncs with voice input
  - `app/api/connection-details/route.ts` - LiveKit room creation endpoint
  - `hooks/useCombinedTranscriptions.ts` - Combines user/agent transcriptions

### Agent (Python)

- **Purpose**: Voice agent that processes speech and controls form
- **Stack**: LiveKit Agents framework, OpenAI (LLM/TTS), Deepgram (STT)
- **File**: `agent/agent.py` - Main agent logic with function calling for form control

### Communication Flow

1. Frontend requests room creation via `/api/connection-details`
2. User connects to LiveKit room with microphone
3. Python agent joins the same room
4. Agent processes speech, extracts form data, and sends updates
5. Frontend receives updates via LiveKit data channels and updates form

## Environment Variables

### Getting LiveKit Credentials

Use the LiveKit CLI to get your project credentials:

```bash
lk app env
```

This will output your LIVEKIT_URL, LIVEKIT_API_KEY, and LIVEKIT_API_SECRET.

### Frontend (.env.local)

- `LIVEKIT_API_KEY` - LiveKit server API key
- `LIVEKIT_API_SECRET` - LiveKit server API secret
- `LIVEKIT_URL` - LiveKit server WebSocket URL

### Agent (agent/.env)

- `LIVEKIT_URL` - Same as frontend
- `LIVEKIT_API_KEY` - Same as frontend
- `LIVEKIT_API_SECRET` - Same as frontend
- `OPENAI_API_KEY` - OpenAI API key for LLM and TTS
- `DEEPGRAM_API_KEY` - Deepgram API key for STT

## Key Implementation Details

### Form-Agent Synchronization

- Agent can read current form values via `get_form_values` function
- Agent updates form fields via `update_form_field` function
- Agent submits form via `submit_form` function
- All updates use LiveKit's data channel with structured JSON messages

### Voice Commands

The agent understands natural language for:

- Filling form fields by name (e.g., "My name is John")
- Reading current form values (e.g., "What's in the email field?")
- Submitting the form (e.g., "Submit the form")

### TypeScript Configuration

- Strict mode enabled for type safety
- Path aliases configured (@/\* for root imports)
- ES2022 target with ESNext module resolution
