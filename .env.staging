# Staging Environment Configuration for Apple Containerization Framework
# LiveKit Voice Agent Form Filler - Staging

# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-staging.livekit.cloud
LIVEKIT_API_KEY=your_staging_api_key
LIVEKIT_API_SECRET=your_staging_api_secret

# Next.js Configuration
NODE_ENV=production
NEXT_PUBLIC_LIVEKIT_URL=wss://your-livekit-staging.livekit.cloud
NEXT_PUBLIC_CONN_DETAILS_ENDPOINT=/api/connection-details
NEXT_PUBLIC_ENVIRONMENT=staging

# Container Configuration
CONTAINER_ENVIRONMENT=staging
CONTAINER_REGISTRY=ghcr.io
CONTAINER_NAMESPACE=livekit-staging

# Monitoring
HEALTH_CHECK_INTERVAL=30
LOG_LEVEL=debug
ENABLE_METRICS=true

# Apple Container Framework Settings
APPLE_CONTAINER_ISOLATION=vm
APPLE_CONTAINER_SECURITY=enhanced
APPLE_CONTAINER_NETWORKING=dedicated
