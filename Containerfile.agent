# Apple Containerization Framework - Python Agent Container
# Multi-stage build optimized for Apple's VM-per-container architecture

# Development stage
FROM python:3.11-slim AS development
WORKDIR /app
RUN apt-get update && apt-get install -y curl git && rm -rf /var/lib/apt/lists/*
COPY agent/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY agent/ .
EXPOSE 8000
CMD ["python", "agent.py", "dev"]

# Production stage
FROM python:3.11-slim AS production
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY agent/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy agent source code
COPY agent/ .

# Download required model files
RUN python agent.py download-files || true

# Create non-root user for security (Apple's framework emphasizes security)
RUN useradd -m -u 1000 agent && chown -R agent:agent /app
USER agent

# Expose any necessary ports (if the agent has HTTP endpoints)
EXPOSE 8000

# Health check - verify agent can import required modules
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD python -c "import livekit.agents; print('Agent healthy')" || exit 1

# Start the agent
CMD ["python", "agent.py", "start"]
