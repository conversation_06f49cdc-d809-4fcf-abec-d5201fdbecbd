# Apple Containerization Framework - Python Agent Container
# Optimized for Apple's VM-per-container architecture

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY agent/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy agent source code
COPY agent/ .

# Create non-root user for security (Apple's framework emphasizes security)
RUN useradd -m -u 1000 agent && chown -R agent:agent /app
USER agent

# Expose any necessary ports (if the agent has HTTP endpoints)
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD python -c "import sys; sys.exit(0)" || exit 1

# Start the agent
CMD ["python", "agent.py", "start"]
