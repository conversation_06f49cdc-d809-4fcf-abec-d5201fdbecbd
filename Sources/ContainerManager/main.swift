import Foundation
import Containerization

@main
struct LiveKitContainerManager {
    static func main() async throws {
        print("🍎 LiveKit Voice Agent - Apple Container Manager")
        
        // Initialize Apple's containerization framework
        let containerRuntime = ContainerRuntime()
        
        // Define container configurations
        let frontendConfig = ContainerConfiguration(
            name: "livekit-frontend",
            image: "livekit-frontend:latest",
            ports: [3000: 3000],
            environment: [
                "NODE_ENV": "production",
                "NEXT_PUBLIC_LIVEKIT_URL": ProcessInfo.processInfo.environment["LIVEKIT_URL"] ?? ""
            ],
            resources: ResourceLimits(
                memory: .megabytes(512),
                cpu: .cores(1)
            )
        )
        
        let agentConfig = ContainerConfiguration(
            name: "livekit-agent", 
            image: "livekit-agent:latest",
            environment: [
                "LIVEKIT_URL": ProcessInfo.processInfo.environment["LIVEKIT_URL"] ?? "",
                "LIVEKIT_API_KEY": ProcessInfo.processInfo.environment["LIVEKIT_API_KEY"] ?? "",
                "LIVEKIT_API_SECRET": ProcessInfo.processInfo.environment["LIVEKIT_API_SECRET"] ?? "",
                "OPENAI_API_KEY": ProcessInfo.processInfo.environment["OPENAI_API_KEY"] ?? "",
                "DEEPGRAM_API_KEY": ProcessInfo.processInfo.environment["DEEPGRAM_API_KEY"] ?? ""
            ],
            resources: ResourceLimits(
                memory: .gigabytes(1),
                cpu: .cores(2)
            )
        )
        
        do {
            // Start containers with Apple's VM-per-container architecture
            print("🚀 Starting frontend container...")
            let frontendContainer = try await containerRuntime.create(frontendConfig)
            try await frontendContainer.start()
            
            print("🤖 Starting agent container...")
            let agentContainer = try await containerRuntime.create(agentConfig)
            try await agentContainer.start()
            
            print("✅ All containers started successfully!")
            print("🌍 Frontend: http://localhost:3000")
            
            // Monitor container health
            try await monitorContainers([frontendContainer, agentContainer])
            
        } catch {
            print("❌ Error: \(error)")
            exit(1)
        }
    }
    
    static func monitorContainers(_ containers: [Container]) async throws {
        while true {
            for container in containers {
                let status = try await container.status()
                print("📊 \(container.name): \(status)")
            }
            try await Task.sleep(nanoseconds: 30_000_000_000) // 30 seconds
        }
    }
}
