import { NextResponse } from "next/server";

// Health check endpoint for Apple's container monitoring
export async function GET() {
  try {
    // Basic health checks
    const healthStatus = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      service: "livekit-voice-agent-frontend",
      version: process.env.npm_package_version || "1.0.0",
      environment: process.env.NODE_ENV || "development",
      checks: {
        livekit: {
          url: process.env.LIVEKIT_URL ? "configured" : "missing",
          status: process.env.LIVEKIT_URL ? "ok" : "error"
        },
        api: {
          key: process.env.LIVEKIT_API_KEY ? "configured" : "missing",
          secret: process.env.LIVEKIT_API_SECRET ? "configured" : "missing",
          status: (process.env.LIVEKIT_API_KEY && process.env.LIVEKIT_API_SECRET) ? "ok" : "error"
        }
      }
    };

    // Determine overall health
    const isHealthy = Object.values(healthStatus.checks).every(check => check.status === "ok");
    
    return NextResponse.json(
      healthStatus,
      { 
        status: isHealthy ? 200 : 503,
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      }
    );
  } catch (error) {
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 503 }
    );
  }
}
