import "@livekit/components-styles";
import { Metadata } from "next";
import dynamic from "next/dynamic";
import { Public_Sans } from "next/font/google";
import "./globals.css";

// Dynamically import performance monitor for dev only
const PerformanceMonitor = dynamic(
  () =>
    import("@/components/PerformanceMonitor").then((mod) => ({ default: mod.PerformanceMonitor })),
  { ssr: false }
);

const publicSans400 = Public_Sans({
  weight: "400",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Voice Assistant - Ice Cream Party Registration",
  description: "Register for our ice cream party using voice commands",
  keywords: "voice assistant, form filling, ice cream party, registration",
  openGraph: {
    title: "Voice Assistant - Ice Cream Party Registration",
    description: "Register for our ice cream party using voice commands",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`h-full ${publicSans400.className}`}>
      <body className="h-full">
        {children}
        {process.env.NODE_ENV === "development" && <PerformanceMonitor />}
      </body>
    </html>
  );
}
