"use client";

import { FormSkeleton } from "@/components/FormSkeleton";
import { logError, logEvent, logger } from "@/lib/logger";
import { Room, RoomEvent } from "livekit-client";
import dynamic from "next/dynamic";
import { useCallback, useEffect, useState } from "react";
import type { ConnectionDetails } from "./api/connection-details/route";

// Dynamically import heavy components
const Form = dynamic(() => import("@/components/Form").then((mod) => ({ default: mod.Form })), {
  ssr: true,
  loading: () => <FormSkeleton />,
});

const VoiceAssistantComponents = dynamic(() => import("@/components/VoiceAssistantComponents"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-gray-400">Loading voice assistant...</div>
    </div>
  ),
});

export default function Page() {
  const [room] = useState(new Room());
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    email: "",
  });
  const [shouldSubmitForm, setShouldSubmitForm] = useState(false);
  const [activeVoiceField, setActiveVoiceField] = useState<keyof typeof formData | null>(null);
  const [lastUpdatedField, setLastUpdatedField] = useState<keyof typeof formData | null>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  const updateFormField = (field: keyof typeof formData, value: string) => {
    logger.debug(`Updating form field: ${field}`, { value });
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Only publish data if the room is connected
    if (room.state === "connected") {
      room.localParticipant.publishData(
        new TextEncoder().encode(JSON.stringify({ field: field, value: value })),
        { topic: "formUpdate", reliable: false }
      );
      logEvent("form_field_updated", { field, value, source: "user" });
    }
  };

  const onConnectButtonClicked = useCallback(async () => {
    try {
      setConnectionError(null);
      setIsReconnecting(false);
      setReconnectAttempts(0);

      logEvent("connection_attempt_started");

      // Generate room connection details
      const url = new URL(
        process.env.NEXT_PUBLIC_CONN_DETAILS_ENDPOINT ?? "/api/connection-details",
        window.location.origin
      );

      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error(`Failed to get connection details: ${response.statusText}`);
      }

      const connectionDetailsData: ConnectionDetails = await response.json();
      logger.info("Connection details received", { roomName: connectionDetailsData.roomName });

      await room.connect(connectionDetailsData.serverUrl, connectionDetailsData.participantToken);
      await room.localParticipant.setMicrophoneEnabled(true);

      logEvent("connection_successful", { roomName: connectionDetailsData.roomName });
    } catch (error) {
      logError(error as Error, "Connection");
      setConnectionError(error instanceof Error ? error.message : "Failed to connect to room");

      // Attempt automatic reconnection
      if (reconnectAttempts < 3) {
        setIsReconnecting(true);
        const backoffTime = 2000 * (reconnectAttempts + 1);
        logger.warn(`Reconnection attempt ${reconnectAttempts + 1} in ${backoffTime}ms`);
        setTimeout(() => {
          setReconnectAttempts((prev) => prev + 1);
          onConnectButtonClicked();
        }, backoffTime);
      } else {
        logEvent("connection_failed_max_retries", { attempts: reconnectAttempts });
      }
    }
  }, [room, reconnectAttempts]);

  useEffect(() => {
    room.on(RoomEvent.MediaDevicesError, onDeviceFailure);

    // Handle disconnection
    room.on(RoomEvent.Disconnected, (reason) => {
      logEvent("room_disconnected", { reason });
      setConnectionError(`Disconnected: ${reason || "Unknown reason"}`);

      // Attempt automatic reconnection for recoverable errors
      if (reason !== "CLIENT_INITIATED" && reconnectAttempts < 3) {
        setIsReconnecting(true);
        logger.info("Attempting automatic reconnection");
        setTimeout(() => {
          setReconnectAttempts((prev) => prev + 1);
          onConnectButtonClicked();
        }, 2000);
      }
    });

    // Handle reconnection
    room.on(RoomEvent.Reconnecting, () => {
      logger.warn("Room reconnecting");
      setIsReconnecting(true);
      setConnectionError("Connection lost. Attempting to reconnect...");
    });

    room.on(RoomEvent.Reconnected, () => {
      logger.info("Room reconnected successfully");
      setIsReconnecting(false);
      setConnectionError(null);
      setReconnectAttempts(0);
      logEvent("room_reconnected");
    });

    room.on(RoomEvent.DataReceived, (payload: Uint8Array, participant) => {
      try {
        const jsonStr = new TextDecoder().decode(payload);
        const data = JSON.parse(jsonStr);
        logger.debug(`Data from ${participant?.identity ?? "unknown"}`, data);

        // Track which field is being updated by voice
        let updatedField: keyof typeof formData | null = null;

        // Use refs to avoid stale closures
        if (data.customer_name !== undefined) {
          updatedField = "name";
        } else if (data.customer_phone !== undefined) {
          updatedField = "phone";
        } else if (data.customer_email !== undefined) {
          updatedField = "email";
        }

        if (updatedField) {
          setActiveVoiceField(updatedField);
          setLastUpdatedField(updatedField);
          // Clear active field after animation
          setTimeout(() => setActiveVoiceField(null), 2000);
          logEvent("voice_field_update", {
            field: updatedField,
            value: data[`customer_${updatedField}`],
          });
        }

        setFormData((prev) => ({
          ...prev,
          name: data.customer_name ?? prev.name,
          phone: data.customer_phone ?? prev.phone,
          email: data.customer_email ?? prev.email,
        }));

        logger.info("Form data updated from voice", {
          name: data.customer_name,
          phone: data.customer_phone,
          email: data.customer_email,
        });

        if (data.should_submit === true) {
          setShouldSubmitForm(true);
          setActiveVoiceField(null);
          logEvent("voice_submit_request");
          return;
        }
      } catch (error) {
        logError(error as Error, "DataReceived");
      }
    });

    return () => {
      room.off(RoomEvent.MediaDevicesError, onDeviceFailure);
      room.off(RoomEvent.Disconnected);
      room.off(RoomEvent.Reconnecting);
      room.off(RoomEvent.Reconnected);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [room, reconnectAttempts, onConnectButtonClicked]);

  return (
    <>
      <a
        href="#form"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-white text-black px-4 py-2 rounded-md z-50"
      >
        Skip to form
      </a>
      <main
        data-lk-theme="default"
        className="h-full grid grid-cols-1 lg:grid-cols-2 bg-[var(--lk-bg)]"
      >
        <div className="lk-room-container max-w-[1024px] w-full mx-auto max-h-[90vh] lg:max-h-screen relative order-2 lg:order-1">
          {connectionError && (
            <div className="absolute top-4 left-4 right-4 z-50 max-w-md mx-auto">
              <div
                className={`p-3 md:p-4 rounded-lg ${isReconnecting ? "bg-yellow-500/20 border border-yellow-500" : "bg-red-500/20 border border-red-500"}`}
              >
                <div className="flex items-center gap-2">
                  {isReconnecting ? (
                    <>
                      <svg className="animate-spin h-5 w-5 text-yellow-500" viewBox="0 0 24 24">
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                          fill="none"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                      <span className="text-yellow-400" role="status" aria-live="assertive">
                        Reconnecting... (Attempt {reconnectAttempts}/3)
                      </span>
                    </>
                  ) : (
                    <>
                      <svg
                        className="h-5 w-5 text-red-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <span className="text-red-400" role="alert" aria-live="assertive">
                        {connectionError}
                      </span>
                    </>
                  )}
                </div>
                {!isReconnecting && reconnectAttempts >= 3 && (
                  <button
                    onClick={() => {
                      setReconnectAttempts(0);
                      onConnectButtonClicked();
                    }}
                    className="mt-2 px-3 py-1 bg-white/20 hover:bg-white/30 rounded text-sm transition-colors"
                    aria-label="Try reconnecting to the voice service"
                  >
                    Try Again
                  </button>
                )}
              </div>
            </div>
          )}
          <VoiceAssistantComponents room={room} onConnectButtonClicked={onConnectButtonClicked} />
        </div>
        <div
          id="form"
          className="flex items-center justify-center p-4 order-1 lg:order-2 min-h-[50vh] lg:min-h-0"
        >
          <Form
            formData={formData}
            updateFormField={updateFormField}
            shouldSubmit={shouldSubmitForm}
            activeVoiceField={activeVoiceField}
            lastUpdatedField={lastUpdatedField}
          />
        </div>
      </main>
    </>
  );
}

function onDeviceFailure(error: Error) {
  logError(error, "MediaDevice");
  alert(
    "Error acquiring camera or microphone permissions. Please make sure you grant the necessary permissions in your browser and reload the tab"
  );
}
