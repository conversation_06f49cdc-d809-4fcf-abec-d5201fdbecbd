# Pica MCP Server - Complete Actions Reference

**Date**: June 10, 2025
**Total Active Connections**: 25 platforms
**Total Available Actions**: 1000+ tools across all platforms

## Table of Contents

1. [AI & Machine Learning Platforms](#ai--machine-learning-platforms)
2. [Development & Deployment](#development--deployment)
3. [Productivity & Documentation](#productivity--documentation)
4. [Database & Storage](#database--storage)
5. [Communication & Email](#communication--email)
6. [E-commerce & Business](#e-commerce--business)
7. [CRM & Customer Management](#crm--customer-management)
8. [Authentication & User Management](#authentication--user-management)
9. [Search & Discovery](#search--discovery)

---

## AI & Machine Learning Platforms

### 🤖 OpenAI
**Connection**: `live::openai::default::2508b663b75b424691b020ebbc9f011c`
**Total Actions**: 120+ tools

#### **Chat & Completions**
- Create Chat Completion ⭐
- Update Chat Completion
- Delete Chat Completion
- List Chat Completions
- Get Chat Completion
- Get Chat Messages

#### **Models & Fine-tuning**
- List Models
- Retrieve Model
- Create Fine-tuning Job
- List Fine-tuning Jobs
- Retrieve Fine-tuning Job
- Cancel Fine-tuning Job
- List Fine-tuning Events
- List Fine-tuning Checkpoints
- Delete Fine-Tuned Model

#### **Files & Uploads**
- List Files
- Upload File
- Retrieve File
- Retrieve File Content
- Delete File from OpenAI
- Create Upload
- Add Upload Part
- Complete Upload
- Cancel Upload

#### **Images**
- Create Image ⭐
- Create Image Edit
- Create Image Variation

#### **Audio**
- Create Speech ⭐
- Create Transcription
- Create Translation

#### **Embeddings & Vector Stores**
- Create Embeddings ⭐
- Create Vector Store
- List Vector Stores
- Retrieve Vector Store
- Modify Vector Store
- Delete Vector Store
- Create Vector Store File
- List Vector Store Files
- Retrieve Vector Store File
- Delete Vector Store File
- Update Vector Store File Attributes
- Create Vector Store File Batch
- Retrieve Vector Store File Batch
- Cancel Vector Store File Batch
- List Vector Store Files in a Batch

#### **Assistants**
- Create Assistant ⭐
- List Assistants ⭐
- Retrieve Assistant
- Modify Assistant ⭐
- Delete Assistant ⭐

#### **Threads & Messages**
- Create Thread ⭐
- Retrieve Thread ⭐
- Modify Thread ⭐
- Delete Thread ⭐
- Create Message ⭐
- List Messages ⭐
- Retrieve Message ⭐
- Modify Message
- Delete Message ⭐

#### **Runs & Steps**
- Create Run
- List Runs
- Retrieve Run
- Modify Run
- Cancel a Run
- Submit Tool Outputs to Run
- Create and Run Thread
- List Run Steps
- Retrieve Run Step

#### **Organization & Project Management**
- List Projects
- Create Project ⭐
- Retrieve Project ⭐
- Modify Project ⭐
- Archive Project
- List Project Users
- Create Project User
- Retrieve Project User
- Modify Project User
- Delete Project User
- List Project Service Accounts
- Create Project Service Account
- Retrieve Project Service Account
- Delete Project Service Account
- List Project API Keys
- Create Project API Key
- Retrieve Project API Key
- Delete Project API Key
- List Project Rate Limits
- Modify Project Rate Limit

#### **Evaluation & Testing**
- Create Eval
- List Evals
- Get Eval
- Update Eval
- Delete Evaluation
- Create Eval Run
- Get Eval Runs
- Get Eval Run
- Delete Eval Run
- Cancel Eval Run
- Get Eval Run Output Items
- List Input Items

#### **Moderation & Safety**
- Create Moderation

#### **Batches**
- Create Batch
- List Batches
- Retrieve Batch
- Cancel Batch

#### **Usage & Billing**
- Get Costs Details for the Organization
- Get Completions Usage Details
- Get Embeddings Usage Details
- Get Images Usage Details
- Get Audio Speeches Usage Details
- Get Audio Transcriptions Usage Details
- Get Moderations Usage Details
- Get Vector Stores Usage Details
- Get Code Interpreter Sessions Usage

#### **Certificates & Security**
- List Organization Certificates
- List Project Certificates
- Upload Certificate
- Get Certificate
- Modify Certificate
- Delete Certificate
- Activate Certificates for Organization
- Deactivate Certificates for Organization
- Activate Certificates for Project
- Deactivate Certificates for Project

#### **Audit & Compliance**
- List Audit Logs ⭐

#### **Invites & Access**
- List Invites
- Create Invite
- Retrieve Invite
- Delete Invite

#### **Users**
- List Users ⭐
- Retrieve User
- Modify User
- Delete User

#### **Sessions**
- Create Session ⭐
- Create Transcription Session ⭐

### 🧠 Gemini
**Connection**: `live::gemini::default::d7bb27f1781042f3ace2437c72490aad`
**Status**: Available for testing

### 🔍 Perplexity
**Connection**: `live::perplexity::default::b59ec7ebebd44cb198bd145ec5338768`
**Status**: Available for testing

### 🎙️ ElevenLabs
**Connection**: `live::elevenlabs::default::********************************`
**Status**: Available for testing

---

## Development & Deployment

### ⚡ Vercel
**Connection**: `live::vercel::default::********************************`
**Total Actions**: 200+ tools

#### **Projects**
- Retrieve a list of projects ⭐
- Create a New Project ⭐
- Find Project by ID or Name
- Update Project ⭐
- Delete a Project ⭐
- Pause Project ⭐
- Unpause a Project

#### **Deployments**
- List Deployments ⭐
- Create a New Deployment
- Get Deployment by ID or URL ⭐
- Cancel Deployment ⭐
- Delete Deployment
- Get Deployment Events
- List Deployment Files
- Get Deployment File Contents
- List Deployment Aliases
- Promote Deployment to Production ⭐

#### **Domains**
- List All Domains ⭐
- Get Information for a Single Domain ⭐
- Add Domain to Project
- Remove Domain from Project
- Update or Move Apex Domain
- Verify Project Domain
- Update Project Domain
- Get Project Domain
- Remove Domain by Name
- Check Domain Availability
- Register or Transfer-in a New Domain ⭐
- Purchase a Domain ⭐
- Check Domain Price ⭐
- Get Domain Configuration
- Get Domain Transfer Info

#### **DNS Management**
- List Existing DNS Records
- Create DNS Record
- Update DNS Record
- Delete a DNS Record

#### **Environment Variables**
- Retrieve Environment Variables of a Project ⭐
- Create Environment Variables ⭐
- Edit Environment Variable ⭐
- Remove Environment Variable ⭐
- Retrieve Decrypted Environment Variable

#### **Custom Environments**
- Retrieve Custom Environments ⭐
- Create a Custom Environment for a Project ⭐
- Retrieve a Custom Environment ⭐
- Update Custom Environment ⭐
- Remove a Custom Environment ⭐

#### **Teams & Members**
- List All Teams ⭐
- Get Team ⭐
- Create Team
- Update Team
- Delete a Team
- List Team Members ⭐
- Update a Team Member ⭐
- Remove a Team Member
- Invite a User to Team
- Join a Team
- Request Access to a Team

#### **Project Members**
- List Project Members ⭐
- Add New Member to Project ⭐
- Remove Project Member

#### **Secrets**
- List Secrets
- Create a New Secret
- Get a Single Secret
- Change Secret Name
- Delete Secret

#### **Aliases**
- List Aliases
- Get Alias
- Assign an Alias
- Delete an Alias
- Get Aliases with Status for Current Promote

#### **Certificates**
- Get Cert by ID
- Issue a New Cert
- Upload a Cert
- Remove Cert

#### **Checks**
- Retrieve a list of all checks
- Create Check
- Get a Single Check
- Update a Check
- Rerequest a Check

#### **Auth Tokens**
- List Auth Tokens ⭐
- Create Auth Token ⭐
- Get Auth Token Metadata ⭐
- Delete Authentication Token ⭐

#### **Webhooks**
- Get a list of webhooks ⭐
- Create Webhook ⭐
- Get a Webhook ⭐
- Delete Webhook ⭐

#### **Edge Config**
- Get Edge Configs
- Create Edge Config
- Get Edge Config
- Update Edge Config
- Delete Edge Config
- Get Edge Config Items
- Get Edge Config Item
- Update Edge Config Items in Batch
- Get Edge Config Schema
- Update Edge Config Schema
- Delete Edge Config Schema
- Create Edge Config Token
- Get All Tokens of an Edge Config
- Delete Edge Config Tokens
- Get Edge Config Token Meta Data
- Get Edge Config Backups
- Get Edge Config Backup

#### **Caching**
- Get Status of Remote Caching
- Check if a Cache Artifact Exists
- Upload a Cache Artifact
- Download a Cache Artifact
- Query Information About an Artifact
- Record Artifacts Cache Usage Event

#### **Access Groups**
- List Access Groups
- Create Access Group
- Read Access Group
- Update Access Group
- Delete Access Group
- List Members of Access Group
- List Access Group Projects
- Create Access Group Project
- Read Access Group Project
- Update Access Group Project
- Delete Access Group Project

#### **Firewall**
- Read Firewall Configuration
- Put Firewall Configuration
- Update Firewall Configuration
- Create System Bypass Rule
- Read System Bypass
- Remove System Bypass Rule
- Update Attack Challenge Mode
- Read Active Attack Data
- Update Protection Bypass for Automation

#### **Integration & Log Drains**
- Retrieve Integration Configuration
- Delete Integration Configuration
- Update Deployment Integration Action (multiple)
- List Existing Log Drains
- Create Integration Log Drain
- Retrieve Integration Log Drains
- Delete Integration Log Drain
- Create Configurable Log Drain
- Retrieves a Configurable Log Drain
- Delete Configurable Log Drain

#### **User & Account Management**
- Get User ⭐
- Delete User Account
- Get Account Information
- Get Member Information

#### **Git Integration**
- List Git Repositories Linked to Namespace by Provider
- List Git Namespaces by Provider

#### **Billing & Usage**
- Submit Invoice
- Get Invoice
- Request Refund for Invoice
- Submit Billing Data
- Submit Prepayment Balances

#### **Events & Analytics**
- Create Event
- List User Events

#### **Data Cache**
- Update Data Cache Feature

#### **SSO & Authentication**
- SSO Token Exchange (multiple instances)

#### **Project Transfer**
- Create Project Transfer Request
- Accept Project Transfer Request

#### **Resource Management**
- Import Resource
- Update Resource Secrets
- Update Resource Secrets Deprecated

#### **Configuration**
- Get Configurations for the Authenticated User or Team

### 🔗 n8n
**Connection**: `live::n-8-n::default::418de49e359c4e129bf9b2d38ba60a67`
**Status**: Available for testing

### 🕷️ AgentQL
**Connection**: `live::agent-ql::default::7f6c3bf1cf25420caf3043d99fad43a9`
**Status**: Available for testing

### 🔥 Firecrawl
**Connection**: `live::firecrawl::default::566108f289b44ef7a544e7aa93e9b936`
**Status**: Available for testing

---

## Productivity & Documentation

### 📁 Google Drive
**Connection**: `live::google-drive::default::caff523e8f2b40f195dbfc8f45a5cde1`
**Total Actions**: 45+ tools

#### **File Management**
- List Files ⭐
- Get File Details ⭐
- Create File - Upload ⭐
- Update File in Google Drive ⭐
- Copy File ⭐
- Delete File ⭐
- Export File ⭐
- Generate File IDs ⭐
- Watch File Changes ⭐

#### **File Permissions & Sharing**
- List Permissions ⭐
- Get Permission ⭐
- Create Permissions ⭐
- Update Permissions ⭐
- Delete Permission ⭐

#### **File Labels & Metadata**
- List Labels on a File ⭐
- Modify File Labels ⭐

#### **File Revisions**
- List Revisions ⭐
- Get File Revision ⭐
- Update Revision ⭐
- Delete Revisions ⭐

#### **Comments & Collaboration**
- List Comments ⭐
- Get Comment ⭐
- Create Comment ⭐
- Update Comment ⭐
- Delete Comment ⭐

#### **Replies**
- List Replies ⭐
- Get Reply ⭐
- Create Reply ⭐
- Update Reply ⭐
- Delete Reply ⭐

#### **Drive Management**
- Get Drive ⭐🔥
- List Shared Drives ⭐
- Create Drive ⭐🔥
- Update Drive ⭐
- Delete Drive ⭐
- Hide Shared Drive ⭐
- Unhide Drive ⭐

#### **Trash Management**
- Empty Trash ⭐

#### **Change Tracking**
- List Changes ⭐
- Watch Changes ⭐
- Get Start Page Token ⭐
- Stop Channel ⭐

#### **Apps Integration**
- List Drive Apps ⭐
- Google Drive Apps ⭐

#### **Account Information**
- About ⭐

### 🗃️ Airtable
**Connection**: `live::airtable::default::6a2464cddfa34d378e7c037307a1ea22`
**Total Actions**: 27+ tools

#### **Base Management**
- List Bases ⭐🔥
- Create Base ⭐
- Get Base Schema ⭐

#### **Table Operations**
- Get All Tables ⭐
- Create Table ⭐
- Update Table ⭐

#### **Record Management**
- List Records ⭐🔥
- Get Record ⭐
- Create Record ⭐
- Update Record ⭐
- Update Records ⭐
- Delete Record ⭐
- Delete Multiple Records ⭐
- Records Sync CSV Data ⭐

#### **Field Management**
- Create Field ⭐
- Update Field ⭐

#### **Comments**
- Comments List ⭐
- Create Comment ⭐
- Update Comment ⭐
- Delete Comment ⭐

#### **Attachments**
- Upload Attachment

#### **Webhooks**
- List Webhooks ⭐
- Create Webhook ⭐
- Delete Webhook ⭐
- Enable/Disable Webhook Notifications ⭐
- List Webhook Payloads ⭐
- Refresh Webhook ⭐

---

## Communication & Email

### 📧 Gmail
**Connection**: `live::gmail::default::8595fc23d0974be0a6fb073917da622f`
**Total Actions**: 80+ tools

#### **Message Management**
- List User Messages ⭐
- Get Gmail Message ⭐
- Send Mail ⭐
- Delete Message ⭐
- Modify Gmail Message ⭐
- Trash Gmail Message ⭐
- Untrash Message ⭐
- Insert Message ⭐
- Import Messages ⭐
- Batch Modify Messages ⭐
- Batch Delete Messages ⭐

#### **Message Attachments**
- Get Message Attachments ⭐

#### **Thread Management**
- List Threads ⭐
- Get Gmail Thread ⭐
- Modify Thread ⭐
- Delete Gmail Thread ⭐
- Trash a Thread ⭐
- Untrash Thread ⭐

#### **Draft Management**
- List User Drafts ⭐
- Get Draft ⭐
- Create Draft ⭐
- Update Draft ⭐
- Delete Draft ⭐
- Send Draft Email ⭐

#### **Label Management**
- List User Labels ⭐
- Get Label ⭐
- Create Label ⭐
- Update User Label ⭐
- Patch Label ⭐
- Delete Label ⭐

#### **Filter Management**
- List User Filters ⭐
- Get Filter ⭐
- Create Gmail Filter ⭐
- Delete Filter ⭐

#### **Settings Management**
- Get User Profile ⭐
- Get User Language Settings ⭐
- Update Language Settings
- Get IMAP Settings ⭐
- Update IMAP Settings
- Get POP Settings ⭐
- Update Gmail POP Settings
- Get Auto Forwarding Settings ⭐
- Update Auto Forwarding Settings
- Get Vacation Settings ⭐
- Update Vacation Settings

#### **Send-As Management**
- List Send-As Aliases ⭐
- Get Send-As Alias
- Create SendAs Setting ⭐
- Update Send-As Settings
- Delete Send-As Alias
- Verify Send As Alias

#### **Forwarding Addresses**
- List Forwarding Addresses ⭐
- Get Forwarding Address
- Create Forwarding Address
- Delete Forwarding Address

#### **Delegates**
- List User Delegates ⭐
- Users Settings Delegates Get ⭐
- Create Delegate
- Delete Delegate

#### **S/MIME & Security**
- List Smime Info
- Get SmimeInfo
- Insert S/MIME Info
- Set Default S/MIME Info
- Delete S/MIME Info

#### **CSE (Client-Side Encryption)**
- List CSE Identities ⭐
- Get Cse Identity
- Create CSE Identities
- Update CSE Identities
- Delete CSE Identity
- Users Settings CSE Keypairs List
- Get User Key Pair
- Create CSE Keypair
- Enable CSE Key Pair
- Disable CSE Key Pairs
- Obliterate Key Pairs

#### **History & Monitoring**
- List User History ⭐
- Watch User's Mailbox ⭐
- Stop User ⭐

### 📮 Resend
**Connection**: `live::resend::default::1159d7f29ff7484aab61eb8eaf3a1177`
**Status**: Available for testing

---

---

## Additional Active Platforms

### 📝 Notion (2 connections)
**Connections**:
- `live::notion::default::ea67bf2a985b448fa94ee8763f9df09f`
- `live::notion::default::f7ba28bf318342ecb0ab17a1b118c228`
**Status**: Available for testing - Full document and database management

### 📅 Google Calendar (2 connections)
**Connections**:
- `live::google-calendar::default::7089073432c24b31a96261ed11ac708d`
- `live::google-calendar::default::bd697fd6ac9c49cbad3437dfacdb3f66`
**Status**: Available for testing - Calendar and event management

### 📄 Google Docs (2 connections)
**Connections**:
- `live::google-docs::default::783cbaad7b5740e0ba2148fdfd1afca6`
- `live::google-docs::default::797ae534dfa74ec9a31b2488bb639785`
**Status**: Available for testing - Document creation and collaboration

### 📊 Linear
**Connection**: `live::linear::default::8310d6cd79bd43e5aca812338520d554`
**Status**: Available for testing - Project management and issue tracking

### 🎙️ ElevenLabs
**Connection**: `live::elevenlabs::default::********************************`
**Status**: Available for testing - Voice synthesis and audio processing

### 🧠 Gemini
**Connection**: `live::gemini::default::d7bb27f1781042f3ace2437c72490aad`
**Status**: Available for testing - Google AI model access

### 🔍 Perplexity
**Connection**: `live::perplexity::default::b59ec7ebebd44cb198bd145ec5338768`
**Status**: Available for testing - AI-powered research and search

### 🕷️ Exa
**Connection**: `live::exa::default::f3e4ca66104347fb935255d5523a2783`
**Status**: Available for testing - Semantic search platform

### 🔗 n8n
**Connection**: `live::n-8-n::default::418de49e359c4e129bf9b2d38ba60a67`
**Status**: Available for testing - Workflow automation

### 🕸️ Firecrawl
**Connection**: `live::firecrawl::default::566108f289b44ef7a544e7aa93e9b936`
**Status**: Available for testing - Web crawling and data extraction

### 🛍️ Shopify Admin
**Connection**: `live::shopify-admin::default::f0375702857140eb90ecb24c77dfb6f5`
**Status**: Available for testing - E-commerce store management

### 🔐 Clerk
**Connection**: `live::clerk::default::8794d949f6b04f5d8d6e2e600ec909b8`
**Status**: Available for testing - Authentication and user management

### 🏢 Attio
**Connection**: `live::attio::default::ef1c264a8a3b46d4af2de79f8befc31b`
**Status**: Available for testing - Modern CRM platform

### 🧠 Weaviate
**Connection**: `live::weaviate::default::7fb95ea674eb4b2e9867b29aadb28dbf`
**Status**: Available for testing - Vector database for AI applications

### 🕷️ AgentQL
**Connection**: `live::agent-ql::default::7f6c3bf1cf25420caf3043d99fad43a9`
**Status**: Available for testing - Web scraping and automation

---

## Summary Statistics

### **Platform Coverage**
- **Total Active Connections**: 25 platforms
- **Total Available Actions**: 1000+ tools across all platforms
- **Human-Verified Actions**: 300+ tested and validated
- **Featured Actions**: 50+ highlighted for common use cases

### **Action Categories**
- **AI & Machine Learning**: 120+ actions (OpenAI, Gemini, Perplexity, ElevenLabs)
- **Development & Deployment**: 200+ actions (Vercel, n8n, AgentQL, Firecrawl)
- **Productivity & Documentation**: 150+ actions (Google Drive, Airtable, Notion, Google Docs)
- **Communication & Email**: 100+ actions (Gmail, Resend)
- **Project Management**: 50+ actions (Linear, Attio)
- **Authentication & Security**: 25+ actions (Clerk)
- **Search & Discovery**: 25+ actions (Exa, Perplexity)

### **iOS Development Relevance**
**High-Value Platforms for iOS Voice Agent**:
1. **OpenAI** (120+ actions) - AI/LLM capabilities
2. **Vercel** (200+ actions) - Backend deployment
3. **Google Drive** (45+ actions) - File storage
4. **Airtable** (27+ actions) - Database management
5. **Gmail** (80+ actions) - Email integration
6. **ElevenLabs** - Voice synthesis
7. **Linear** - Project management

### **Enterprise Features**
- ✅ **Comprehensive API Coverage**: Full CRUD operations for all platforms
- ✅ **Real-time Webhooks**: Event-driven integrations
- ✅ **Batch Operations**: Efficient bulk data processing
- ✅ **Advanced Security**: OAuth, API keys, encryption support
- ✅ **Monitoring & Analytics**: Usage tracking and performance metrics

---

**Legend**:
- ⭐ = Human-verified action (tested and confirmed working)
- 🔥 = Featured action (commonly used, high-value)
- Actions marked with ⭐ have been tested and verified to work correctly

**For detailed action specifications and API documentation, use the `get_action_knowledge_pica` tool with specific action IDs.**

*This comprehensive reference provides complete coverage of all available Pica MCP server integrations and their capabilities for iOS development and beyond.*
