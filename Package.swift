// swift-tools-version: 5.9
// Apple's Swift Container Plugin Integration

import PackageDescription

let package = Package(
    name: "LiveKitVoiceAgentContainers",
    platforms: [
        .macOS(.v14)
    ],
    products: [
        .executable(name: "container-manager", targets: ["ContainerManager"])
    ],
    dependencies: [
        // Apple's official containerization framework
        .package(url: "https://github.com/apple/containerization", from: "1.0.0"),
        // Swift Container Plugin for building containers
        .package(url: "https://github.com/apple/swift-container-plugin", from: "1.0.0"),
    ],
    targets: [
        .executableTarget(
            name: "ContainerManager",
            dependencies: [
                .product(name: "Containerization", package: "containerization"),
            ],
            path: "Sources/ContainerManager"
        ),
    ]
)
