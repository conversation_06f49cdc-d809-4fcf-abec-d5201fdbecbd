// swift-tools-version: 5.9
// Apple's Swift Container Plugin Integration (Demo Version)

import PackageDescription

let package = Package(
    name: "LiveKitVoiceAgentContainers",
    platforms: [
        .macOS(.v14)
    ],
    products: [
        .executable(name: "container-manager", targets: ["ContainerManager"])
    ],
    dependencies: [
        // Using Foundation for demo - Apple's framework will be available later
        // .package(url: "https://github.com/apple/containerization", from: "1.0.0"),
        // .package(url: "https://github.com/apple/swift-container-plugin", from: "1.0.0"),
    ],
    targets: [
        .executableTarget(
            name: "ContainerManager",
            dependencies: [
                // .product(name: "Containerization", package: "containerization"),
            ],
            path: "Sources/ContainerManager"
        ),
    ]
)
