import { formatPhone<PERSON><PERSON>ber, validateEmail, validateName, validatePhone } from "./validation";

describe("validateEmail", () => {
  it("should validate correct email addresses", () => {
    expect(validateEmail("<EMAIL>")).toEqual({ isValid: true });
    expect(validateEmail("<EMAIL>")).toEqual({ isValid: true });
    expect(validateEmail("<EMAIL>")).toEqual({ isValid: true });
  });

  it("should reject invalid email addresses", () => {
    expect(validateEmail("")).toEqual({ isValid: false, error: "Email is required" });
    expect(validateEmail("not-an-email")).toEqual({
      isValid: false,
      error: "Please enter a valid email address",
    });
    expect(validateEmail("@example.com")).toEqual({
      isValid: false,
      error: "Please enter a valid email address",
    });
    expect(validateEmail("user@")).toEqual({
      isValid: false,
      error: "Please enter a valid email address",
    });
    expect(validateEmail("user@domain")).toEqual({
      isValid: false,
      error: "Please enter a valid email address",
    });
  });
});

describe("validatePhone", () => {
  it("should validate correct US phone numbers", () => {
    expect(validatePhone("1234567890")).toEqual({ isValid: true });
    expect(validatePhone("(*************")).toEqual({ isValid: true });
    expect(validatePhone("************")).toEqual({ isValid: true });
    expect(validatePhone("11234567890")).toEqual({ isValid: true });
    expect(validatePhone("+****************")).toEqual({ isValid: true });
  });

  it("should validate international phone numbers", () => {
    expect(validatePhone("441234567890")).toEqual({ isValid: true }); // UK
    expect(validatePhone("33123456789")).toEqual({ isValid: true }); // France
  });

  it("should reject invalid phone numbers", () => {
    expect(validatePhone("")).toEqual({ isValid: false, error: "Phone number is required" });
    expect(validatePhone("123")).toEqual({
      isValid: false,
      error: "Please enter a valid phone number",
    });
    expect(validatePhone("12345678901234567")).toEqual({
      isValid: false,
      error: "Please enter a valid phone number",
    });
  });
});

describe("validateName", () => {
  it("should validate correct names", () => {
    expect(validateName("John")).toEqual({ isValid: true });
    expect(validateName("Mary Jane")).toEqual({ isValid: true });
    expect(validateName("Jean-Pierre")).toEqual({ isValid: true });
    expect(validateName("O'Brien")).toEqual({ isValid: true });
  });

  it("should reject invalid names", () => {
    expect(validateName("")).toEqual({ isValid: false, error: "Name is required" });
    expect(validateName("J")).toEqual({
      isValid: false,
      error: "Name must be at least 2 characters long",
    });
    expect(validateName("123")).toEqual({
      isValid: false,
      error: "Name can only contain letters, spaces, hyphens, and apostrophes",
    });
    expect(validateName("John@Doe")).toEqual({
      isValid: false,
      error: "Name can only contain letters, spaces, hyphens, and apostrophes",
    });
  });
});

describe("formatPhoneNumber", () => {
  it("should format US phone numbers", () => {
    expect(formatPhoneNumber("1234567890")).toBe("(*************");
    expect(formatPhoneNumber("11234567890")).toBe("+****************");
  });

  it("should return original for non-US numbers", () => {
    expect(formatPhoneNumber("441234567890")).toBe("441234567890");
    expect(formatPhoneNumber("123")).toBe("123");
  });
});
