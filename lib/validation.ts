export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export function validateEmail(email: string): ValidationResult {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!email) {
    return { isValid: false, error: "Email is required" };
  }

  if (!emailRegex.test(email)) {
    return { isValid: false, error: "Please enter a valid email address" };
  }

  return { isValid: true };
}

export function validatePhone(phone: string): ValidationResult {
  // Remove all non-numeric characters for validation
  const cleanedPhone = phone.replace(/\D/g, "");

  if (!phone) {
    return { isValid: false, error: "Phone number is required" };
  }

  // Check for common phone number lengths (10-15 digits internationally)
  if (cleanedPhone.length < 10 || cleanedPhone.length > 15) {
    return { isValid: false, error: "Please enter a valid phone number" };
  }

  // Basic format check for US numbers (10 digits)
  if (cleanedPhone.length === 10) {
    const usPhoneRegex = /^\d{10}$/;
    if (!usPhoneRegex.test(cleanedPhone)) {
      return { isValid: false, error: "Please enter a valid 10-digit phone number" };
    }
  }

  // Basic format check for US numbers with country code (11 digits starting with 1)
  if (cleanedPhone.length === 11 && cleanedPhone.startsWith("1")) {
    const usPhoneWithCodeRegex = /^1\d{10}$/;
    if (!usPhoneWithCodeRegex.test(cleanedPhone)) {
      return { isValid: false, error: "Please enter a valid phone number" };
    }
  }

  return { isValid: true };
}

export function validateName(name: string): ValidationResult {
  if (!name) {
    return { isValid: false, error: "Name is required" };
  }

  if (name.trim().length < 2) {
    return { isValid: false, error: "Name must be at least 2 characters long" };
  }

  // Check for basic name pattern (letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-Z\s\-']+$/;
  if (!nameRegex.test(name)) {
    return {
      isValid: false,
      error: "Name can only contain letters, spaces, hyphens, and apostrophes",
    };
  }

  return { isValid: true };
}

export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, "");

  // Format as US phone number if it's 10 or 11 digits
  if (cleaned.length === 10) {
    return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  } else if (cleaned.length === 11 && cleaned.startsWith("1")) {
    return cleaned.replace(/1(\d{3})(\d{3})(\d{4})/, "+1 ($1) $2-$3");
  }

  // Return original for international numbers
  return phone;
}
