type LogLevel = "debug" | "info" | "warn" | "error";

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: unknown;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private isDevelopment = process.env.NODE_ENV === "development";

  private log(level: LogLevel, message: string, data?: unknown) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
    };

    // Store in memory
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Console output in development
    if (this.isDevelopment) {
      const style = this.getLogStyle(level);
      console.log(`%c[${level.toUpperCase()}] ${message}`, style, data || "");
    }

    // Send to monitoring service in production
    if (!this.isDevelopment && level === "error") {
      this.sendToMonitoring(entry);
    }
  }

  private getLogStyle(level: LogLevel): string {
    const styles = {
      debug: "color: #888",
      info: "color: #2563eb",
      warn: "color: #f59e0b",
      error: "color: #dc2626; font-weight: bold",
    };
    return styles[level];
  }

  private sendToMonitoring(entry: LogEntry) {
    // In production, you would send to a service like Sentry, LogRocket, etc.
    // For now, we'll just store in sessionStorage
    try {
      const stored = sessionStorage.getItem("app_errors") || "[]";
      const errors = JSON.parse(stored);
      errors.push(entry);
      // Keep only last 50 errors
      if (errors.length > 50) {
        errors.splice(0, errors.length - 50);
      }
      sessionStorage.setItem("app_errors", JSON.stringify(errors));
    } catch (e) {
      console.error("Failed to store error log:", e);
    }
  }

  debug(message: string, data?: unknown) {
    this.log("debug", message, data);
  }

  info(message: string, data?: unknown) {
    this.log("info", message, data);
  }

  warn(message: string, data?: unknown) {
    this.log("warn", message, data);
  }

  error(message: string, data?: unknown) {
    this.log("error", message, data);
  }

  getLogs(level?: LogLevel): LogEntry[] {
    if (level) {
      return this.logs.filter((log) => log.level === level);
    }
    return [...this.logs];
  }

  clearLogs() {
    this.logs = [];
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Singleton instance
export const logger = new Logger();

// Utility functions for common logging scenarios
export const logEvent = (event: string, data?: unknown) => {
  logger.info(`Event: ${event}`, data);
};

export const logError = (error: Error | string, context?: string) => {
  const message = error instanceof Error ? error.message : error;
  const stack = error instanceof Error ? error.stack : undefined;
  logger.error(`${context ? `[${context}] ` : ""}${message}`, { stack });
};

export const logPerformance = (operation: string, duration: number) => {
  logger.info(`Performance: ${operation} took ${duration}ms`);
};

// Hook for measuring component render time
export const useRenderTime = (componentName: string) => {
  const startTime = performance.now();

  if (process.env.NODE_ENV === "development") {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    import("react").then(({ useEffect }) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      useEffect(() => {
        const endTime = performance.now();
        logPerformance(`${componentName} render`, endTime - startTime);
      });
    });
  }
};
