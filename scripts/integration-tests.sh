#!/bin/bash

# Integration tests for Apple Containerization Framework deployment
# Comprehensive testing of the LiveKit Voice Agent system

set -e

FRONTEND_URL="http://localhost:3001"  # Staging URL
TIMEOUT=30

echo "🧪 Running integration tests for LiveKit Voice Agent..."

# Test 1: Frontend Health Check
echo "🏥 Testing frontend health..."
response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$FRONTEND_URL/api/health")
if [ "$response" = "200" ]; then
  health_status=$(jq -r '.status' /tmp/health_response.json)
  if [ "$health_status" = "healthy" ]; then
    echo "✅ Frontend health check passed"
  else
    echo "❌ Frontend health check failed: $health_status"
    exit 1
  fi
else
  echo "❌ Frontend health endpoint returned $response"
  exit 1
fi

# Test 2: Frontend Page Load
echo "🌐 Testing frontend page load..."
response=$(curl -s -w "%{http_code}" -o /dev/null "$FRONTEND_URL")
if [ "$response" = "200" ]; then
  echo "✅ Frontend page loads successfully"
else
  echo "❌ Frontend page load failed: $response"
  exit 1
fi

# Test 3: API Connection Details Endpoint
echo "🔗 Testing connection details API..."
response=$(curl -s -w "%{http_code}" -o /tmp/connection_response.json "$FRONTEND_URL/api/connection-details")
if [ "$response" = "200" ]; then
  server_url=$(jq -r '.serverUrl' /tmp/connection_response.json)
  if [ "$server_url" != "null" ] && [ -n "$server_url" ]; then
    echo "✅ Connection details API working"
  else
    echo "❌ Connection details API returned invalid data"
    exit 1
  fi
else
  echo "❌ Connection details API failed: $response"
  exit 1
fi

# Test 4: Container Network Connectivity
echo "🌐 Testing container network connectivity..."
if container exec livekit-staging-frontend ping -c 1 livekit-staging-agent > /dev/null 2>&1; then
  echo "✅ Frontend can reach agent container"
else
  echo "❌ Frontend cannot reach agent container"
  exit 1
fi

if container exec livekit-staging-agent ping -c 1 livekit-staging-redis > /dev/null 2>&1; then
  echo "✅ Agent can reach Redis container"
else
  echo "❌ Agent cannot reach Redis container"
  exit 1
fi

# Test 5: Redis Connectivity
echo "📦 Testing Redis connectivity..."
if container exec livekit-staging-redis redis-cli ping | grep -q "PONG"; then
  echo "✅ Redis is responding"
else
  echo "❌ Redis is not responding"
  exit 1
fi

# Test 6: Agent Module Import Test
echo "🤖 Testing agent module imports..."
if container exec livekit-staging-agent python -c "
import livekit.agents
import livekit.plugins.openai
import livekit.plugins.deepgram
print('All modules imported successfully')
" > /dev/null 2>&1; then
  echo "✅ Agent modules import successfully"
else
  echo "❌ Agent module import failed"
  exit 1
fi

# Test 7: Environment Variables Test
echo "🔧 Testing environment variables..."
frontend_env=$(container exec livekit-staging-frontend printenv NODE_ENV)
if [ "$frontend_env" = "production" ] || [ "$frontend_env" = "staging" ]; then
  echo "✅ Frontend environment variables set correctly"
else
  echo "❌ Frontend environment variables not set correctly"
  exit 1
fi

# Test 8: Container Resource Limits
echo "📊 Testing container resource limits..."
frontend_memory=$(container stats --no-stream --format "{{.MemUsage}}" livekit-staging-frontend | cut -d'/' -f1)
agent_memory=$(container stats --no-stream --format "{{.MemUsage}}" livekit-staging-agent | cut -d'/' -f1)

echo "Frontend memory usage: $frontend_memory"
echo "Agent memory usage: $agent_memory"

# Test 9: Log Output Test
echo "📝 Testing log output..."
frontend_logs=$(container logs --tail 10 livekit-staging-frontend 2>&1)
if echo "$frontend_logs" | grep -q "ready\|started\|listening"; then
  echo "✅ Frontend logs show service is running"
else
  echo "⚠️  Frontend logs don't show clear startup messages"
fi

agent_logs=$(container logs --tail 10 livekit-staging-agent 2>&1)
if echo "$agent_logs" | grep -q "ready\|started\|agent"; then
  echo "✅ Agent logs show service is running"
else
  echo "⚠️  Agent logs don't show clear startup messages"
fi

# Test 10: Performance Test
echo "🚀 Running basic performance test..."
if command -v ab &> /dev/null; then
  echo "Running Apache Bench test..."
  ab -n 50 -c 5 -q "$FRONTEND_URL/api/health" > /tmp/ab_results.txt 2>&1
  
  if grep -q "Failed requests:        0" /tmp/ab_results.txt; then
    echo "✅ Performance test passed - no failed requests"
  else
    echo "⚠️  Performance test had some failed requests"
    cat /tmp/ab_results.txt
  fi
else
  echo "⚠️  Apache Bench not available, skipping performance test"
fi

# Test 11: Security Test - Check for non-root users
echo "🔒 Testing security configurations..."
frontend_user=$(container exec livekit-staging-frontend whoami)
if [ "$frontend_user" != "root" ]; then
  echo "✅ Frontend running as non-root user: $frontend_user"
else
  echo "⚠️  Frontend running as root user"
fi

agent_user=$(container exec livekit-staging-agent whoami)
if [ "$agent_user" != "root" ]; then
  echo "✅ Agent running as non-root user: $agent_user"
else
  echo "⚠️  Agent running as root user"
fi

# Test 12: Container Restart Test
echo "🔄 Testing container restart capability..."
echo "Restarting frontend container..."
container restart livekit-staging-frontend

# Wait for restart
sleep 5
timeout 30 bash -c 'until curl -f '"$FRONTEND_URL"'/api/health; do sleep 1; done'

if [ $? -eq 0 ]; then
  echo "✅ Container restart test passed"
else
  echo "❌ Container restart test failed"
  exit 1
fi

# Generate test report
echo "📊 Generating test report..."
cat > integration-test-report.json << EOF
{
  "test_run": {
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "environment": "staging",
    "frontend_url": "$FRONTEND_URL"
  },
  "tests": {
    "frontend_health": "passed",
    "frontend_page_load": "passed",
    "api_connection_details": "passed",
    "container_network": "passed",
    "redis_connectivity": "passed",
    "agent_modules": "passed",
    "environment_variables": "passed",
    "resource_limits": "checked",
    "log_output": "checked",
    "performance": "$([ -f /tmp/ab_results.txt ] && echo 'passed' || echo 'skipped')",
    "security": "checked",
    "container_restart": "passed"
  },
  "summary": {
    "total_tests": 12,
    "passed": 9,
    "warnings": 3,
    "failed": 0
  }
}
EOF

echo "✅ All integration tests completed successfully!"
echo "📄 Test report saved to integration-test-report.json"
echo ""
echo "🎉 LiveKit Voice Agent staging deployment is ready for production!"
