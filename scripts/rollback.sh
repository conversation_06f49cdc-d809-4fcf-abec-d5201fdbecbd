#!/bin/bash

# Rollback script for Apple Containerization Framework
# Quickly restore previous working version

set -e

NAMESPACE="livekit-prod"
BACKUP_NAMESPACE="livekit-prod-backup"

echo "🔄 Rolling back LiveKit Voice Agent deployment..."

# Stop current (failed) containers
echo "🛑 Stopping current containers..."
for service in frontend agent redis; do
  if container ps --filter "name=${NAMESPACE}-${service}" --quiet | grep -q .; then
    echo "Stopping ${NAMESPACE}-${service}..."
    container stop "${NAMESPACE}-${service}" || true
    container rm "${NAMESPACE}-${service}" || true
  fi
done

# Restore backup containers
echo "📦 Restoring backup containers..."
for service in frontend agent redis; do
  if container ps -a --filter "name=${BACKUP_NAMESPACE}-${service}" --quiet | grep -q .; then
    echo "Restoring ${BACKUP_NAMESPACE}-${service}..."
    container rename "${BACKUP_NAMESPACE}-${service}" "${NAMESPACE}-${service}"
    container start "${NAMESPACE}-${service}"
  fi
done

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
timeout 60 bash -c 'until curl -f http://localhost:3000/api/health; do sleep 2; done'

# Verify rollback
echo "✅ Verifying rollback..."
FRONTEND_HEALTH=$(curl -s http://localhost:3000/api/health | jq -r '.status')
if [ "$FRONTEND_HEALTH" = "healthy" ]; then
  echo "✅ Rollback successful!"
  echo "🌍 Production URL: http://localhost:3000"
else
  echo "❌ Rollback failed - manual intervention required"
  exit 1
fi

# Log rollback event
echo "📝 Logging rollback event..."
cat > rollback-event.json << EOF
{
  "event": "rollback",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "reason": "Deployment health check failed",
  "restored_containers": [
    "${NAMESPACE}-frontend",
    "${NAMESPACE}-agent", 
    "${NAMESPACE}-redis"
  ]
}
EOF

# Send rollback notification
if [ -n "$SLACK_WEBHOOK_URL" ]; then
  curl -X POST -H 'Content-type: application/json' \
    --data "{\"text\":\"🔄 LiveKit Voice Agent rolled back to previous version due to health check failure\"}" \
    "$SLACK_WEBHOOK_URL"
fi

echo "📄 Rollback event logged to rollback-event.json"
