#!/bin/bash

# Development setup with Apple's Containerization Framework
# Optimized for fast iteration and debugging

set -e

echo "🛠️  Setting up development environment with Apple containers"

# Check prerequisites
if ! command -v container &> /dev/null; then
    echo "Installing Apple container CLI..."
    brew install apple/containerization/container
fi

# Create development network
container network create livekit-dev-network || true

# Build development images with hot reload
echo "📦 Building development containers..."

# Frontend with hot reload
container build -f Containerfile.frontend \
  --target development \
  -t livekit-frontend:dev .

# Agent with development mode
container build -f Containerfile.agent \
  --target development \
  -t livekit-agent:dev .

# Start development containers
echo "🚀 Starting development containers..."

# Frontend with volume mounting for hot reload
container run -d \
  --name livekit-frontend-dev \
  --network livekit-dev-network \
  -p 3000:3000 \
  -v "$(pwd):/app" \
  -v "/app/node_modules" \
  --env-file .env.local \
  livekit-frontend:dev \
  bun dev

# Agent with development mode
container run -d \
  --name livekit-agent-dev \
  --network livekit-dev-network \
  -v "$(pwd)/agent:/app" \
  --env-file agent/.env \
  livekit-agent:dev \
  python agent.py dev

echo "✅ Development environment ready!"
echo "🌍 Frontend: http://localhost:3000"
echo "📝 Logs: container logs -f livekit-frontend-dev"
echo "🔧 Debug: container exec -it livekit-agent-dev bash"
