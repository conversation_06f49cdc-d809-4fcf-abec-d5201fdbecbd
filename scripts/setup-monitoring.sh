#!/bin/bash

# Monitoring setup for Apple Containerization Framework
# Sets up comprehensive monitoring and alerting

set -e

NAMESPACE="livekit-prod"
MONITORING_NAMESPACE="livekit-monitoring"

echo "📡 Setting up monitoring for LiveKit Voice Agent..."

# Create monitoring network
container network create $MONITORING_NAMESPACE || true

# Deploy Prometheus for metrics collection
echo "📊 Deploying Prometheus..."
cat > prometheus.yml << EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'apple-containers'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'livekit-frontend'
    static_configs:
      - targets: ['${NAMESPACE}-frontend:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 10s

  - job_name: 'livekit-agent'
    static_configs:
      - targets: ['${NAMESPACE}-agent:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
EOF

container run -d \
  --name "${MONITORING_NAMESPACE}-prometheus" \
  --network $MONITORING_NAMESPACE \
  -p 9090:9090 \
  -v "$(pwd)/prometheus.yml:/etc/prometheus/prometheus.yml" \
  --restart unless-stopped \
  prom/prometheus

# Deploy Grafana for visualization
echo "📈 Deploying Grafana..."
container run -d \
  --name "${MONITORING_NAMESPACE}-grafana" \
  --network $MONITORING_NAMESPACE \
  -p 3001:3000 \
  -e GF_SECURITY_ADMIN_PASSWORD=admin123 \
  -v grafana-storage:/var/lib/grafana \
  --restart unless-stopped \
  grafana/grafana

# Deploy AlertManager for alerting
echo "🚨 Deploying AlertManager..."
cat > alertmanager.yml << EOF
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://localhost:5001/webhook'
EOF

container run -d \
  --name "${MONITORING_NAMESPACE}-alertmanager" \
  --network $MONITORING_NAMESPACE \
  -p 9093:9093 \
  -v "$(pwd)/alertmanager.yml:/etc/alertmanager/alertmanager.yml" \
  --restart unless-stopped \
  prom/alertmanager

# Create alert rules
cat > alert_rules.yml << EOF
groups:
- name: livekit-alerts
  rules:
  - alert: ContainerDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Container {{ \$labels.instance }} is down"
      description: "Container {{ \$labels.instance }} has been down for more than 1 minute."

  - alert: HighCPUUsage
    expr: container_cpu_usage_percent > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage on {{ \$labels.container_name }}"
      description: "CPU usage is above 80% for more than 5 minutes."

  - alert: HighMemoryUsage
    expr: container_memory_usage_percent > 90
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High memory usage on {{ \$labels.container_name }}"
      description: "Memory usage is above 90% for more than 5 minutes."

  - alert: FrontendHealthCheckFailed
    expr: frontend_health_status != 1
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Frontend health check failed"
      description: "Frontend health check has been failing for more than 2 minutes."
EOF

# Deploy custom metrics exporter for Apple containers
echo "🔍 Deploying Apple Container Metrics Exporter..."
cat > container-exporter.py << 'EOF'
#!/usr/bin/env python3
import time
import subprocess
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse

class MetricsHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/metrics':
            metrics = self.get_container_metrics()
            self.send_response(200)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(metrics.encode())
        else:
            self.send_response(404)
            self.end_headers()

    def get_container_metrics(self):
        try:
            # Get container stats using Apple's container CLI
            result = subprocess.run(['container', 'stats', '--no-stream', '--format', 'json'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                stats = json.loads(result.stdout)
                metrics = []
                
                for container in stats:
                    name = container.get('name', 'unknown')
                    cpu = float(container.get('cpu_percent', 0))
                    memory = float(container.get('memory_percent', 0))
                    
                    metrics.append(f'container_cpu_usage_percent{{container_name="{name}"}} {cpu}')
                    metrics.append(f'container_memory_usage_percent{{container_name="{name}"}} {memory}')
                    metrics.append(f'container_up{{container_name="{name}"}} 1')
                
                return '\n'.join(metrics)
        except Exception as e:
            print(f"Error getting metrics: {e}")
        
        return "# No metrics available"

if __name__ == '__main__':
    server = HTTPServer(('0.0.0.0', 8080), MetricsHandler)
    print("Starting metrics server on port 8080...")
    server.serve_forever()
EOF

container run -d \
  --name "${MONITORING_NAMESPACE}-exporter" \
  --network $MONITORING_NAMESPACE \
  -p 8080:8080 \
  -v "$(pwd)/container-exporter.py:/app/exporter.py" \
  -v /var/run/container.sock:/var/run/container.sock \
  --restart unless-stopped \
  python:3.11-slim \
  python /app/exporter.py

# Wait for services to be ready
echo "⏳ Waiting for monitoring services..."
timeout 60 bash -c 'until curl -f http://localhost:9090/-/ready; do sleep 2; done'
timeout 60 bash -c 'until curl -f http://localhost:3001/api/health; do sleep 2; done'

# Import Grafana dashboards
echo "📊 Setting up Grafana dashboards..."
sleep 10  # Wait for Grafana to fully start

# Create datasource
curl -X POST \
  ************************************/api/datasources \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Prometheus",
    "type": "prometheus",
    "url": "http://prometheus:9090",
    "access": "proxy",
    "isDefault": true
  }' || true

echo "✅ Monitoring setup complete!"
echo "📊 Prometheus: http://localhost:9090"
echo "📈 Grafana: http://localhost:3001 (admin/admin123)"
echo "🚨 AlertManager: http://localhost:9093"
echo "🔍 Metrics Exporter: http://localhost:8080/metrics"

# Create monitoring summary
cat > monitoring-setup.json << EOF
{
  "monitoring_services": {
    "prometheus": {
      "url": "http://localhost:9090",
      "status": "running"
    },
    "grafana": {
      "url": "http://localhost:3001",
      "credentials": "admin/admin123",
      "status": "running"
    },
    "alertmanager": {
      "url": "http://localhost:9093",
      "status": "running"
    },
    "metrics_exporter": {
      "url": "http://localhost:8080/metrics",
      "status": "running"
    }
  },
  "setup_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

echo "📄 Monitoring setup info saved to monitoring-setup.json"
