#!/bin/bash

# Production deployment script for Apple Containerization Framework
# Blue-green deployment with automatic rollback on failure

set -e

ENVIRONMENT="production"
NAMESPACE="livekit-prod"
REGISTRY="ghcr.io"
IMAGE_TAG="${GITHUB_SHA:-latest}"
BACKUP_NAMESPACE="livekit-prod-backup"

echo "🚀 Deploying LiveKit Voice Agent to Production"
echo "Environment: $ENVIRONMENT"
echo "Image Tag: $IMAGE_TAG"

# Ensure Apple container system is running
container system start

# Create backup of current production
echo "💾 Creating backup of current production..."
if container ps --filter "name=${NAMESPACE}-" --quiet | grep -q .; then
  echo "📦 Backing up current containers..."
  
  # Stop and rename current containers to backup
  for service in frontend agent redis; do
    if container ps --filter "name=${NAMESPACE}-${service}" --quiet | grep -q .; then
      container stop "${NAMESPACE}-${service}" || true
      container rename "${NAMESPACE}-${service}" "${BACKUP_NAMESPACE}-${service}" || true
    fi
  done
fi

# Create production network
echo "🌐 Setting up production environment..."
container network create $NAMESPACE || true

# Deploy Redis for production
echo "📦 Deploying Redis..."
container run -d \
  --name "${NAMESPACE}-redis" \
  --network $NAMESPACE \
  -p 6379:6379 \
  -v redis-data:/data \
  --restart unless-stopped \
  --memory 256m \
  --cpus 0.5 \
  redis:7-alpine redis-server --appendonly yes

# Wait for Redis to be ready
echo "⏳ Waiting for Redis..."
timeout 30 bash -c 'until container exec ${NAMESPACE}-redis redis-cli ping; do sleep 1; done'

# Deploy Agent with production settings
echo "🤖 Deploying Agent..."
container run -d \
  --name "${NAMESPACE}-agent" \
  --network $NAMESPACE \
  --env-file agent/.env.production \
  -e REDIS_URL="redis://${NAMESPACE}-redis:6379" \
  -e ENVIRONMENT=$ENVIRONMENT \
  -e LOG_LEVEL=info \
  -v agent-logs:/app/logs \
  --restart unless-stopped \
  --memory 2g \
  --cpus 2 \
  $REGISTRY/$GITHUB_REPOSITORY/agent:$IMAGE_TAG

# Wait for Agent to be ready
echo "⏳ Waiting for Agent..."
timeout 120 bash -c 'until container exec ${NAMESPACE}-agent python -c "import livekit.agents; print(\"ready\")"; do sleep 2; done'

# Deploy Frontend with production settings
echo "🎨 Deploying Frontend..."
container run -d \
  --name "${NAMESPACE}-frontend" \
  --network $NAMESPACE \
  -p 3000:3000 \
  --env-file .env.production \
  -e ENVIRONMENT=$ENVIRONMENT \
  -e NODE_ENV=production \
  --restart unless-stopped \
  --memory 512m \
  --cpus 1 \
  $REGISTRY/$GITHUB_REPOSITORY/frontend:$IMAGE_TAG

# Wait for Frontend to be ready
echo "⏳ Waiting for Frontend..."
timeout 120 bash -c 'until curl -f http://localhost:3000/api/health; do sleep 2; done'

# Comprehensive health checks
echo "🏥 Running comprehensive health checks..."

# Frontend health check
FRONTEND_HEALTH=$(curl -s http://localhost:3000/api/health | jq -r '.status')
if [ "$FRONTEND_HEALTH" != "healthy" ]; then
  echo "❌ Frontend health check failed - Rolling back..."
  ./scripts/rollback.sh
  exit 1
fi

# Agent health check (if it has health endpoint)
if container exec "${NAMESPACE}-agent" python -c "import requests; requests.get('http://localhost:8000/health')" 2>/dev/null; then
  AGENT_HEALTH=$(container exec "${NAMESPACE}-agent" curl -s http://localhost:8000/health | jq -r '.status')
  if [ "$AGENT_HEALTH" != "healthy" ]; then
    echo "❌ Agent health check failed - Rolling back..."
    ./scripts/rollback.sh
    exit 1
  fi
fi

# Load test
echo "🔥 Running load test..."
if command -v ab &> /dev/null; then
  ab -n 100 -c 10 http://localhost:3000/api/health > load-test-results.txt
  if [ $? -ne 0 ]; then
    echo "❌ Load test failed - Rolling back..."
    ./scripts/rollback.sh
    exit 1
  fi
fi

# Performance monitoring
echo "📊 Collecting performance metrics..."
container stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" \
  "${NAMESPACE}-frontend" "${NAMESPACE}-agent" "${NAMESPACE}-redis" > performance-metrics.txt

# Cleanup backup containers if deployment successful
echo "🧹 Cleaning up backup containers..."
for service in frontend agent redis; do
  if container ps -a --filter "name=${BACKUP_NAMESPACE}-${service}" --quiet | grep -q .; then
    container rm -f "${BACKUP_NAMESPACE}-${service}" || true
  fi
done

# Setup monitoring and alerting
echo "📡 Setting up monitoring..."
./scripts/setup-monitoring.sh

echo "✅ Production deployment complete!"
echo "🌍 Production URL: http://localhost:3000"
echo "📊 Metrics: container stats"
echo "📝 Logs: container logs -f ${NAMESPACE}-frontend"

# Store deployment info
cat > production-deployment.json << EOF
{
  "environment": "$ENVIRONMENT",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "image_tag": "$IMAGE_TAG",
  "commit": "$GITHUB_SHA",
  "containers": [
    "${NAMESPACE}-frontend",
    "${NAMESPACE}-agent", 
    "${NAMESPACE}-redis"
  ],
  "urls": {
    "frontend": "http://localhost:3000",
    "health": "http://localhost:3000/api/health"
  },
  "resources": {
    "frontend": {"memory": "512m", "cpu": "1"},
    "agent": {"memory": "2g", "cpu": "2"},
    "redis": {"memory": "256m", "cpu": "0.5"}
  }
}
EOF

echo "📄 Production deployment info saved to production-deployment.json"

# Send deployment notification (if configured)
if [ -n "$SLACK_WEBHOOK_URL" ]; then
  curl -X POST -H 'Content-type: application/json' \
    --data "{\"text\":\"✅ LiveKit Voice Agent deployed to production successfully! Image: $IMAGE_TAG\"}" \
    "$SLACK_WEBHOOK_URL"
fi
