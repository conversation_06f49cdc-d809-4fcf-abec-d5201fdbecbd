#!/bin/bash

# Comprehensive health check script for Apple Containerization Framework
# Monitors all aspects of the LiveKit Voice Agent deployment

set -e

NAMESPACE="livekit-prod"
FRONTEND_URL="http://localhost:3000"
TIMEOUT=30

echo "🏥 Running comprehensive health check for LiveKit Voice Agent..."

# Initialize health status
OVERALL_HEALTH="healthy"
ISSUES=()

# Function to add issue
add_issue() {
  ISSUES+=("$1")
  OVERALL_HEALTH="unhealthy"
}

# Function to check container status
check_container_status() {
  local container_name="$1"
  local service_name="$2"
  
  echo "🔍 Checking $service_name container..."
  
  if container ps --filter "name=$container_name" --format "{{.Status}}" | grep -q "Up"; then
    echo "✅ $service_name container is running"
    
    # Check container health if health check is configured
    health_status=$(container inspect "$container_name" --format "{{.State.Health.Status}}" 2>/dev/null || echo "none")
    if [ "$health_status" = "healthy" ]; then
      echo "✅ $service_name health check passed"
    elif [ "$health_status" = "unhealthy" ]; then
      add_issue "$service_name health check failed"
    elif [ "$health_status" = "starting" ]; then
      echo "⏳ $service_name health check starting..."
    fi
    
  else
    add_issue "$service_name container is not running"
  fi
}

# Function to check service endpoint
check_endpoint() {
  local url="$1"
  local service_name="$2"
  local expected_status="${3:-200}"
  
  echo "🌐 Checking $service_name endpoint: $url"
  
  response=$(curl -s -w "%{http_code}" -o /tmp/endpoint_response.json "$url" --max-time $TIMEOUT)
  if [ "$response" = "$expected_status" ]; then
    echo "✅ $service_name endpoint responding correctly"
  else
    add_issue "$service_name endpoint returned $response instead of $expected_status"
  fi
}

# Function to check resource usage
check_resource_usage() {
  local container_name="$1"
  local service_name="$2"
  local cpu_threshold="${3:-80}"
  local memory_threshold="${4:-90}"
  
  echo "📊 Checking $service_name resource usage..."
  
  stats=$(container stats --no-stream --format "{{.CPUPerc}},{{.MemPerc}}" "$container_name" 2>/dev/null || echo "0.00%,0.00%")
  cpu_usage=$(echo "$stats" | cut -d',' -f1 | sed 's/%//')
  memory_usage=$(echo "$stats" | cut -d',' -f2 | sed 's/%//')
  
  echo "CPU: ${cpu_usage}%, Memory: ${memory_usage}%"
  
  if (( $(echo "$cpu_usage > $cpu_threshold" | bc -l) )); then
    add_issue "$service_name CPU usage is high: ${cpu_usage}%"
  fi
  
  if (( $(echo "$memory_usage > $memory_threshold" | bc -l) )); then
    add_issue "$service_name memory usage is high: ${memory_usage}%"
  fi
}

# Function to check logs for errors
check_logs_for_errors() {
  local container_name="$1"
  local service_name="$2"
  
  echo "📝 Checking $service_name logs for errors..."
  
  error_count=$(container logs --tail 100 "$container_name" 2>&1 | grep -i "error\|exception\|failed\|fatal" | wc -l)
  if [ "$error_count" -gt 5 ]; then
    add_issue "$service_name has $error_count recent errors in logs"
  elif [ "$error_count" -gt 0 ]; then
    echo "⚠️  $service_name has $error_count recent errors (within acceptable range)"
  else
    echo "✅ $service_name logs show no recent errors"
  fi
}

# Start health checks
echo "🚀 Starting health check at $(date)"
echo "=" * 60

# 1. Check container statuses
check_container_status "${NAMESPACE}-frontend" "Frontend"
check_container_status "${NAMESPACE}-agent" "Agent"
check_container_status "${NAMESPACE}-redis" "Redis"

echo ""

# 2. Check service endpoints
check_endpoint "$FRONTEND_URL/api/health" "Frontend Health"
check_endpoint "$FRONTEND_URL" "Frontend Main Page"
check_endpoint "$FRONTEND_URL/api/connection-details" "Connection Details API"

echo ""

# 3. Check resource usage
check_resource_usage "${NAMESPACE}-frontend" "Frontend" 70 80
check_resource_usage "${NAMESPACE}-agent" "Agent" 80 85
check_resource_usage "${NAMESPACE}-redis" "Redis" 60 70

echo ""

# 4. Check logs for errors
check_logs_for_errors "${NAMESPACE}-frontend" "Frontend"
check_logs_for_errors "${NAMESPACE}-agent" "Agent"
check_logs_for_errors "${NAMESPACE}-redis" "Redis"

echo ""

# 5. Check network connectivity between containers
echo "🌐 Checking inter-container connectivity..."
if container exec "${NAMESPACE}-frontend" ping -c 1 "${NAMESPACE}-agent" > /dev/null 2>&1; then
  echo "✅ Frontend can reach Agent"
else
  add_issue "Frontend cannot reach Agent container"
fi

if container exec "${NAMESPACE}-agent" ping -c 1 "${NAMESPACE}-redis" > /dev/null 2>&1; then
  echo "✅ Agent can reach Redis"
else
  add_issue "Agent cannot reach Redis container"
fi

echo ""

# 6. Check Redis functionality
echo "📦 Checking Redis functionality..."
if container exec "${NAMESPACE}-redis" redis-cli ping | grep -q "PONG"; then
  echo "✅ Redis is responding to ping"
  
  # Test basic Redis operations
  if container exec "${NAMESPACE}-redis" redis-cli set healthcheck "$(date)" > /dev/null 2>&1; then
    echo "✅ Redis write operation successful"
    
    if container exec "${NAMESPACE}-redis" redis-cli get healthcheck > /dev/null 2>&1; then
      echo "✅ Redis read operation successful"
    else
      add_issue "Redis read operation failed"
    fi
  else
    add_issue "Redis write operation failed"
  fi
else
  add_issue "Redis is not responding to ping"
fi

echo ""

# 7. Check disk space
echo "💾 Checking disk space..."
disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$disk_usage" -gt 90 ]; then
  add_issue "Disk usage is critical: ${disk_usage}%"
elif [ "$disk_usage" -gt 80 ]; then
  echo "⚠️  Disk usage is high: ${disk_usage}%"
else
  echo "✅ Disk usage is normal: ${disk_usage}%"
fi

echo ""

# 8. Check system load
echo "⚡ Checking system load..."
load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
cpu_count=$(nproc)
load_threshold=$(echo "$cpu_count * 2" | bc)

if (( $(echo "$load_avg > $load_threshold" | bc -l) )); then
  add_issue "System load is high: $load_avg (threshold: $load_threshold)"
else
  echo "✅ System load is normal: $load_avg"
fi

echo ""

# 9. Check Apple Container System Status
echo "🍎 Checking Apple Container System..."
if container system status > /dev/null 2>&1; then
  echo "✅ Apple Container System is running"
else
  add_issue "Apple Container System is not running properly"
fi

echo ""

# Generate health report
echo "📊 Generating health report..."

# Calculate uptime for each container
frontend_uptime=$(container inspect "${NAMESPACE}-frontend" --format "{{.State.StartedAt}}" 2>/dev/null || echo "unknown")
agent_uptime=$(container inspect "${NAMESPACE}-agent" --format "{{.State.StartedAt}}" 2>/dev/null || echo "unknown")
redis_uptime=$(container inspect "${NAMESPACE}-redis" --format "{{.State.StartedAt}}" 2>/dev/null || echo "unknown")

# Create detailed health report
cat > health-report.json << EOF
{
  "health_check": {
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "overall_status": "$OVERALL_HEALTH",
    "issues_count": ${#ISSUES[@]}
  },
  "containers": {
    "frontend": {
      "status": "$(container ps --filter "name=${NAMESPACE}-frontend" --format "{{.Status}}" 2>/dev/null || echo "not found")",
      "uptime": "$frontend_uptime",
      "health": "$(container inspect "${NAMESPACE}-frontend" --format "{{.State.Health.Status}}" 2>/dev/null || echo "none")"
    },
    "agent": {
      "status": "$(container ps --filter "name=${NAMESPACE}-agent" --format "{{.Status}}" 2>/dev/null || echo "not found")",
      "uptime": "$agent_uptime",
      "health": "$(container inspect "${NAMESPACE}-agent" --format "{{.State.Health.Status}}" 2>/dev/null || echo "none")"
    },
    "redis": {
      "status": "$(container ps --filter "name=${NAMESPACE}-redis" --format "{{.Status}}" 2>/dev/null || echo "not found")",
      "uptime": "$redis_uptime",
      "health": "$(container inspect "${NAMESPACE}-redis" --format "{{.State.Health.Status}}" 2>/dev/null || echo "none")"
    }
  },
  "system": {
    "disk_usage": "${disk_usage}%",
    "load_average": "$load_avg",
    "cpu_count": $cpu_count
  },
  "issues": $(printf '%s\n' "${ISSUES[@]}" | jq -R . | jq -s .)
}
EOF

echo "=" * 60
echo "🏥 Health Check Summary"
echo "=" * 60

if [ "$OVERALL_HEALTH" = "healthy" ]; then
  echo "✅ Overall Status: HEALTHY"
  echo "🎉 All systems are operating normally!"
else
  echo "❌ Overall Status: UNHEALTHY"
  echo "🚨 Issues found:"
  for issue in "${ISSUES[@]}"; do
    echo "   - $issue"
  done
fi

echo ""
echo "📄 Detailed report saved to health-report.json"
echo "🌍 Frontend URL: $FRONTEND_URL"
echo "📊 Monitor: container stats"

# Send health status to monitoring system (if configured)
if [ -n "$HEALTH_WEBHOOK_URL" ]; then
  curl -X POST -H 'Content-type: application/json' \
    --data "{\"status\":\"$OVERALL_HEALTH\",\"issues\":${#ISSUES[@]},\"timestamp\":\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"}" \
    "$HEALTH_WEBHOOK_URL" || true
fi

# Exit with appropriate code
if [ "$OVERALL_HEALTH" = "healthy" ]; then
  exit 0
else
  exit 1
fi
