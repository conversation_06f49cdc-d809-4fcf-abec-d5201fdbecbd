#!/bin/bash

# Staging deployment script for Apple Containerization Framework
# Deploys to staging environment with monitoring and rollback capabilities

set -e

ENVIRONMENT="staging"
NAMESPACE="livekit-staging"
REGISTRY="ghcr.io"
IMAGE_TAG="${GITHUB_SHA:-latest}"

echo "🚀 Deploying LiveKit Voice Agent to Staging"
echo "Environment: $ENVIRONMENT"
echo "Image Tag: $IMAGE_TAG"

# Ensure Apple container system is running
container system start

# Create staging namespace/network
echo "🌐 Setting up staging environment..."
container network create $NAMESPACE || true

# Deploy Redis for staging
echo "📦 Deploying Redis..."
container run -d \
  --name "${NAMESPACE}-redis" \
  --network $NAMESPACE \
  -p 6380:6379 \
  --restart unless-stopped \
  redis:7-alpine

# Wait for Redis to be ready
echo "⏳ Waiting for Redis..."
timeout 30 bash -c 'until container exec ${NAMESPACE}-redis redis-cli ping; do sleep 1; done'

# Deploy Agent
echo "🤖 Deploying Agent..."
container run -d \
  --name "${NAMESPACE}-agent" \
  --network $NAMESPACE \
  --env-file agent/.env.staging \
  -e REDIS_URL="redis://${NAMESPACE}-redis:6379" \
  -e ENVIRONMENT=$ENVIRONMENT \
  --restart unless-stopped \
  $REGISTRY/$GITHUB_REPOSITORY/agent:$IMAGE_TAG

# Wait for Agent to be ready
echo "⏳ Waiting for Agent..."
timeout 60 bash -c 'until container exec ${NAMESPACE}-agent python -c "import livekit.agents; print(\"ready\")"; do sleep 2; done'

# Deploy Frontend
echo "🎨 Deploying Frontend..."
container run -d \
  --name "${NAMESPACE}-frontend" \
  --network $NAMESPACE \
  -p 3001:3000 \
  --env-file .env.staging \
  -e ENVIRONMENT=$ENVIRONMENT \
  -e NEXT_PUBLIC_API_URL="http://localhost:3001" \
  --restart unless-stopped \
  $REGISTRY/$GITHUB_REPOSITORY/frontend:$IMAGE_TAG

# Wait for Frontend to be ready
echo "⏳ Waiting for Frontend..."
timeout 60 bash -c 'until curl -f http://localhost:3001/api/health; do sleep 2; done'

# Verify deployment
echo "✅ Verifying deployment..."
container ps --filter "name=${NAMESPACE}-"

# Health checks
echo "🏥 Running health checks..."
FRONTEND_HEALTH=$(curl -s http://localhost:3001/api/health | jq -r '.status')
if [ "$FRONTEND_HEALTH" != "healthy" ]; then
  echo "❌ Frontend health check failed"
  exit 1
fi

# Performance baseline
echo "📊 Collecting performance baseline..."
container stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" \
  "${NAMESPACE}-frontend" "${NAMESPACE}-agent" "${NAMESPACE}-redis"

echo "✅ Staging deployment complete!"
echo "🌍 Staging URL: http://localhost:3001"
echo "📝 Logs: container logs -f ${NAMESPACE}-frontend"
echo "🔧 Debug: container exec -it ${NAMESPACE}-agent bash"

# Store deployment info
cat > deployment-info.json << EOF
{
  "environment": "$ENVIRONMENT",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "image_tag": "$IMAGE_TAG",
  "commit": "$GITHUB_SHA",
  "containers": [
    "${NAMESPACE}-frontend",
    "${NAMESPACE}-agent", 
    "${NAMESPACE}-redis"
  ],
  "urls": {
    "frontend": "http://localhost:3001",
    "health": "http://localhost:3001/api/health"
  }
}
EOF

echo "📄 Deployment info saved to deployment-info.json"
