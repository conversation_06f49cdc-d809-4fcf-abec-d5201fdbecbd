# 🍎 Apple Containers Quick Start Guide

## 🚀 One-Command Setup

```bash
# Complete setup and start
./setup-apple-containers.sh && ./apple-container-setup.sh
```

## 📋 Prerequisites Checklist

- [ ] macOS 26+ (recommended)
- [ ] Apple Container CLI installed
- [ ] Xcode 16+ or Swift toolchain
- [ ] Node.js 18+
- [ ] Python 3.11+

## ⚡ Quick Commands

### Setup & Installation
```bash
# Initial setup
./setup-apple-containers.sh

# Development environment
./scripts/dev-setup.sh

# Production deployment
./scripts/deploy-production.sh
```

### Container Management
```bash
# Start all services
container-compose up -d

# View status
container ps

# View logs
container logs -f livekit-frontend

# Stop all services
container-compose down
```

### Swift Container Manager
```bash
# Deploy stack
swift run container-manager deploy

# Check status
swift run container-manager status

# View logs
swift run container-manager logs frontend

# Scale service
swift run container-manager scale agent 3
```

### Monitoring & Health
```bash
# Health check
./scripts/health-check.sh

# Setup monitoring
./scripts/setup-monitoring.sh

# Integration tests
./scripts/integration-tests.sh
```

## 🌐 Access Points

| Service | URL | Credentials |
|---------|-----|-------------|
| Frontend | http://localhost:3000 | - |
| Grafana | http://localhost:3001 | admin/admin123 |
| Prometheus | http://localhost:9090 | - |
| AlertManager | http://localhost:9093 | - |

## 🔧 Environment Configuration

### Required API Keys

Update these files with your API keys:

**`.env.local`**
```env
LIVEKIT_URL=wss://your-livekit.livekit.cloud
LIVEKIT_API_KEY=your_api_key
LIVEKIT_API_SECRET=your_api_secret
```

**`agent/.env`**
```env
LIVEKIT_URL=wss://your-livekit.livekit.cloud
LIVEKIT_API_KEY=your_api_key
LIVEKIT_API_SECRET=your_api_secret
OPENAI_API_KEY=your_openai_key
DEEPGRAM_API_KEY=your_deepgram_key
```

## 🚨 Troubleshooting

### Container CLI Issues
```bash
# Install/reinstall
brew tap apple/containerization
brew install container

# Start system
container system start
```

### Permission Issues
```bash
# Fix permissions
sudo container system start
chmod +x scripts/*.sh
```

### Network Issues
```bash
# Reset network
container network rm livekit-network
container network create livekit-network
```

### Build Issues
```bash
# Clean rebuild
container system prune -a
./apple-container-setup.sh
```

## 📊 Performance Monitoring

### Key Metrics to Watch
- Container startup time (should be <1 second)
- Memory usage per container
- CPU utilization
- Network throughput

### Apple Container Advantages
- 20-40x faster than Docker Desktop
- VM-per-container isolation
- Hardware-level security
- Sub-second startup times
- Dedicated networking

## 🔄 Development Workflow

1. **Start Development**
   ```bash
   ./scripts/dev-setup.sh
   ```

2. **Make Changes**
   - Frontend: Hot reload enabled
   - Agent: Restart container for changes

3. **Test Changes**
   ```bash
   ./scripts/integration-tests.sh
   ```

4. **Deploy to Staging**
   ```bash
   ./scripts/deploy-staging.sh
   ```

5. **Deploy to Production**
   ```bash
   ./scripts/deploy-production.sh
   ```

## 🛡️ Security Features

- **VM Isolation**: Each container in separate VM
- **Non-root Execution**: All containers run as non-root
- **Minimal Images**: Reduced attack surface
- **Network Isolation**: Dedicated IPs per container
- **Hardware Security**: Apple Silicon security features

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale frontend
swift run container-manager scale frontend 3

# Scale agent
swift run container-manager scale agent 5
```

### Vertical Scaling
Edit container configurations in `container-compose.yml`:
```yaml
resources:
  memory: 2g
  cpu: 2
```

## 🔗 Useful Links

- [Apple Containerization Docs](https://developer.apple.com/containerization/)
- [LiveKit Documentation](https://docs.livekit.io/)
- [Project Documentation](./APPLE_CONTAINERIZATION.md)
- [CI/CD Pipeline](./.github/workflows/apple-container-ci.yml)

## 💡 Pro Tips

1. **Use Swift Manager**: More powerful than basic CLI
2. **Monitor Resources**: Apple containers are efficient but monitor usage
3. **Leverage VM Isolation**: Perfect for security-critical applications
4. **Use Dedicated Networks**: Better performance than port forwarding
5. **Enable Monitoring**: Comprehensive observability included

## 🆘 Getting Help

1. Check logs: `container logs <container-name>`
2. Run health check: `./scripts/health-check.sh`
3. View documentation: `APPLE_CONTAINERIZATION.md`
4. Check container status: `container ps -a`
5. Monitor resources: `container stats`

---

**🎉 You're now running LiveKit Voice Agent with Apple's revolutionary containerization technology!**
