import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { logEvent, logger } from "@/lib/logger";
import { formatPhoneNumber, validateEmail, validateName, validatePhone } from "@/lib/validation";
import confetti from "canvas-confetti";
import dynamic from "next/dynamic";
import { useCallback, useEffect, useRef, useState } from "react";

// Dynamically import ConfirmationDialog since it's only shown on submit
const ConfirmationDialog = dynamic(
  () =>
    import("@/components/ConfirmationDialog").then((mod) => ({ default: mod.ConfirmationDialog })),
  { ssr: false }
);

interface FormData {
  name: string;
  phone: string;
  email: string;
}

interface FormProps {
  formData: FormData;
  updateFormField: (field: keyof FormData, value: string) => void;
  shouldSubmit?: boolean;
  activeVoiceField?: keyof FormData | null;
  lastUpdatedField?: keyof FormData | null;
}

// Debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function Form({
  formData,
  updateFormField,
  shouldSubmit = false,
  activeVoiceField,
  lastUpdatedField,
}: FormProps) {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [localFormData, setLocalFormData] = useState<FormData>(formData);
  const isUpdatingFromParent = useRef(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [touched, setTouched] = useState<Record<keyof FormData, boolean>>({
    name: false,
    phone: false,
    email: false,
  });
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // Sync localFormData with incoming formData changes
  useEffect(() => {
    isUpdatingFromParent.current = true;
    setLocalFormData(formData);
    // Reset the flag after the update
    setTimeout(() => {
      isUpdatingFromParent.current = false;
    }, 0);
  }, [formData]);

  // Debounce the local form data changes
  const debouncedFormData = useDebounce(localFormData, 500); // 500ms delay

  // Update parent component when debounced value changes
  useEffect(() => {
    // Only update parent if the change came from user input
    if (!isUpdatingFromParent.current) {
      Object.entries(debouncedFormData).forEach(([field, value]) => {
        if (value !== formData[field as keyof FormData]) {
          updateFormField(field as keyof FormData, value);
        }
      });
    }
  }, [debouncedFormData, formData, updateFormField]);

  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<FormData> = {};

    const nameValidation = validateName(localFormData.name);
    if (!nameValidation.isValid) {
      newErrors.name = nameValidation.error;
    }

    const phoneValidation = validatePhone(localFormData.phone);
    if (!phoneValidation.isValid) {
      newErrors.phone = phoneValidation.error;
    }

    const emailValidation = validateEmail(localFormData.email);
    if (!emailValidation.isValid) {
      newErrors.email = emailValidation.error;
    }

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;

    if (!isValid) {
      logger.debug("Form validation failed", newErrors);
      logEvent("form_validation_failed", { errors: newErrors });
    }

    return isValid;
  }, [localFormData]);

  const handleSubmit = (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    // Mark all fields as touched
    setTouched({ name: true, phone: true, email: true });

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Show confirmation dialog
    setShowConfirmDialog(true);
  };

  const handleConfirmSubmit = () => {
    setShowConfirmDialog(false);
    logEvent("form_submitted", localFormData);

    // Get button position
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const x = rect.left + rect.width / 2;
      const y = rect.top + rect.height / 2;

      // Convert to percentage of window size
      const xPercent = x / window.innerWidth;
      const yPercent = y / window.innerHeight;

      // Trigger confetti from button position
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { x: xPercent, y: yPercent },
      });
    }

    // Clear form fields and errors
    isUpdatingFromParent.current = true;
    setLocalFormData({ name: "", phone: "", email: "" });
    updateFormField("name", "");
    updateFormField("phone", "");
    updateFormField("email", "");
    setErrors({});
    setTouched({ name: false, phone: false, email: false });
    setTimeout(() => {
      isUpdatingFromParent.current = false;
    }, 0);

    // Set submitted state
    setIsSubmitted(true);
    logger.info("Form submitted successfully", localFormData);
  };

  const handleCancelSubmit = () => {
    setShowConfirmDialog(false);
    logEvent("form_submission_cancelled");
  };

  // Watch for shouldSubmit changes
  useEffect(() => {
    if (shouldSubmit && !isSubmitted) {
      // For voice commands, validate first then show dialog
      setTouched({ name: true, phone: true, email: true });
      if (validateForm()) {
        setShowConfirmDialog(true);
      }
    }
  }, [shouldSubmit, isSubmitted, validateForm]);

  return (
    <div className="w-full max-w-md p-4 sm:p-6 space-y-4 sm:space-y-6 bg-zinc-800 rounded-lg shadow-lg">
      {isSubmitted ? (
        <div className="text-center space-y-4">
          <p className="text-xl">You&apos;re signed up for the ice cream party! 🍦</p>
          <p className="text-sm text-gray-400">We&apos;ll send you the details soon.</p>
        </div>
      ) : (
        <>
          <h2 className="text-xl sm:text-2xl font-bold text-center">Ice Cream Party</h2>
          <form className="space-y-3 sm:space-y-4" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <label
                htmlFor="name"
                className="text-sm font-medium flex items-start sm:items-center gap-2 flex-wrap"
              >
                Name
                {activeVoiceField === "name" && (
                  <span
                    className="inline-flex items-center gap-1 text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full animate-pulse"
                    aria-live="polite"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path
                        fillRule="evenodd"
                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Voice input
                  </span>
                )}
                {!activeVoiceField && lastUpdatedField === "name" && (
                  <span
                    className="inline-flex items-center gap-1 text-xs bg-green-500/10 text-green-400 px-2 py-1 rounded-full"
                    aria-live="polite"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Updated
                  </span>
                )}
              </label>
              <Input
                className={`text-black transition-all duration-300 ${touched.name && errors.name ? "border-red-500" : ""} ${activeVoiceField === "name" ? "ring-2 ring-green-500 border-green-500" : ""}`}
                id="name"
                type="text"
                required
                placeholder="Enter your full name"
                value={localFormData.name}
                aria-label="Name"
                aria-invalid={touched.name && !!errors.name}
                aria-describedby={touched.name && errors.name ? "name-error" : undefined}
                onChange={(e) => {
                  setLocalFormData((prev) => ({ ...prev, name: e.target.value }));
                  if (touched.name) {
                    const validation = validateName(e.target.value);
                    setErrors((prev) => ({ ...prev, name: validation.error }));
                  }
                }}
                onBlur={() => {
                  setTouched((prev) => ({ ...prev, name: true }));
                  const validation = validateName(localFormData.name);
                  setErrors((prev) => ({ ...prev, name: validation.error }));
                }}
              />
              {touched.name && errors.name && (
                <p id="name-error" role="alert" className="text-xs text-red-500 mt-1">
                  {errors.name}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <label
                htmlFor="phone"
                className="text-sm font-medium flex items-start sm:items-center gap-2 flex-wrap"
              >
                Phone Number
                {activeVoiceField === "phone" && (
                  <span
                    className="inline-flex items-center gap-1 text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full animate-pulse"
                    aria-live="polite"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path
                        fillRule="evenodd"
                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Voice input
                  </span>
                )}
                {!activeVoiceField && lastUpdatedField === "phone" && (
                  <span
                    className="inline-flex items-center gap-1 text-xs bg-green-500/10 text-green-400 px-2 py-1 rounded-full"
                    aria-live="polite"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Updated
                  </span>
                )}
              </label>
              <Input
                className={`text-black transition-all duration-300 ${touched.phone && errors.phone ? "border-red-500" : ""} ${activeVoiceField === "phone" ? "ring-2 ring-green-500 border-green-500" : ""}`}
                id="phone"
                type="tel"
                required
                placeholder="(*************"
                value={localFormData.phone}
                aria-label="Phone Number"
                aria-invalid={touched.phone && !!errors.phone}
                aria-describedby={touched.phone && errors.phone ? "phone-error" : undefined}
                onChange={(e) => {
                  const formatted = formatPhoneNumber(e.target.value);
                  setLocalFormData((prev) => ({ ...prev, phone: formatted }));
                  if (touched.phone) {
                    const validation = validatePhone(e.target.value);
                    setErrors((prev) => ({ ...prev, phone: validation.error }));
                  }
                }}
                onBlur={() => {
                  setTouched((prev) => ({ ...prev, phone: true }));
                  const validation = validatePhone(localFormData.phone);
                  setErrors((prev) => ({ ...prev, phone: validation.error }));
                }}
              />
              {touched.phone && errors.phone && (
                <p id="phone-error" role="alert" className="text-xs text-red-500 mt-1">
                  {errors.phone}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <label
                htmlFor="email"
                className="text-sm font-medium flex items-start sm:items-center gap-2 flex-wrap"
              >
                Email
                {activeVoiceField === "email" && (
                  <span
                    className="inline-flex items-center gap-1 text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full animate-pulse"
                    aria-live="polite"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path
                        fillRule="evenodd"
                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Voice input
                  </span>
                )}
                {!activeVoiceField && lastUpdatedField === "email" && (
                  <span
                    className="inline-flex items-center gap-1 text-xs bg-green-500/10 text-green-400 px-2 py-1 rounded-full"
                    aria-live="polite"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Updated
                  </span>
                )}
              </label>
              <Input
                className={`text-black transition-all duration-300 ${touched.email && errors.email ? "border-red-500" : ""} ${activeVoiceField === "email" ? "ring-2 ring-green-500 border-green-500" : ""}`}
                id="email"
                type="email"
                required
                placeholder="<EMAIL>"
                value={localFormData.email}
                aria-label="Email"
                aria-invalid={touched.email && !!errors.email}
                aria-describedby={touched.email && errors.email ? "email-error" : undefined}
                onChange={(e) => {
                  setLocalFormData((prev) => ({ ...prev, email: e.target.value }));
                  if (touched.email) {
                    const validation = validateEmail(e.target.value);
                    setErrors((prev) => ({ ...prev, email: validation.error }));
                  }
                }}
                onBlur={() => {
                  setTouched((prev) => ({ ...prev, email: true }));
                  const validation = validateEmail(localFormData.email);
                  setErrors((prev) => ({ ...prev, email: validation.error }));
                }}
              />
              {touched.email && errors.email && (
                <p id="email-error" role="alert" className="text-xs text-red-500 mt-1">
                  {errors.email}
                </p>
              )}
            </div>
            <Button
              ref={buttonRef}
              type="submit"
              className="w-full"
              aria-label="Submit registration form"
            >
              Submit
            </Button>
          </form>
        </>
      )}
      <ConfirmationDialog
        isOpen={showConfirmDialog}
        onConfirm={handleConfirmSubmit}
        onCancel={handleCancelSubmit}
        formData={localFormData}
      />
    </div>
  );
}
