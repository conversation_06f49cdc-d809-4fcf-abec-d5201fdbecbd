import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Form } from "./Form";

// Mock confetti
const mockConfetti = jest.fn();
jest.mock("canvas-confetti", () => mockConfetti);

describe("Form", () => {
  const mockUpdateFormField = jest.fn();
  const defaultProps = {
    formData: { name: "", phone: "", email: "" },
    updateFormField: mockUpdateFormField,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders all form fields", () => {
    render(<Form {...defaultProps} />);

    expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /submit/i })).toBeInTheDocument();
  });

  it("displays initial form data", () => {
    const props = {
      ...defaultProps,
      formData: {
        name: "John Doe",
        phone: "(*************",
        email: "<EMAIL>",
      },
    };

    render(<Form {...props} />);

    expect(screen.getByDisplayValue("John Doe")).toBeInTheDocument();
    expect(screen.getByDisplayValue("(*************")).toBeInTheDocument();
    expect(screen.getByDisplayValue("<EMAIL>")).toBeInTheDocument();
  });

  it("shows validation errors when fields are touched and invalid", async () => {
    const user = userEvent.setup();
    render(<Form {...defaultProps} />);

    const nameInput = screen.getByLabelText(/name/i);
    const emailInput = screen.getByLabelText(/email/i);

    // Touch fields without entering valid data
    await user.click(nameInput);
    await user.type(nameInput, "J");
    await user.click(emailInput);

    await waitFor(() => {
      expect(screen.getByText("Name must be at least 2 characters long")).toBeInTheDocument();
    });
  });

  it("shows validation error for invalid email", async () => {
    const user = userEvent.setup();
    render(<Form {...defaultProps} />);

    const emailInput = screen.getByLabelText(/email/i);

    await user.type(emailInput, "invalid-email");
    await user.tab(); // blur the field

    await waitFor(() => {
      expect(screen.getByText("Please enter a valid email address")).toBeInTheDocument();
    });
  });

  it("formats phone number as user types", async () => {
    const user = userEvent.setup();
    render(<Form {...defaultProps} />);

    const phoneInput = screen.getByLabelText(/phone number/i);

    await user.type(phoneInput, "1234567890");

    await waitFor(() => {
      expect(phoneInput).toHaveValue("(*************");
    });
  });

  it("prevents form submission with invalid data", async () => {
    const user = userEvent.setup();
    render(<Form {...defaultProps} />);

    const submitButton = screen.getByRole("button", { name: /submit/i });

    await user.click(submitButton);

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText("Name is required")).toBeInTheDocument();
      expect(screen.getByText("Phone number is required")).toBeInTheDocument();
      expect(screen.getByText("Email is required")).toBeInTheDocument();
    });

    // Confetti should not be called
    expect(mockConfetti).not.toHaveBeenCalled();
  });

  it("submits form with valid data and shows success message", async () => {
    const user = userEvent.setup();
    render(<Form {...defaultProps} />);

    await user.type(screen.getByLabelText(/name/i), "John Doe");
    await user.type(screen.getByLabelText(/phone number/i), "1234567890");
    await user.type(screen.getByLabelText(/email/i), "<EMAIL>");

    const submitButton = screen.getByRole("button", { name: /submit/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/you're signed up for the ice cream party/i)).toBeInTheDocument();
      expect(mockConfetti).toHaveBeenCalled();
    });
  });

  it("calls updateFormField with debounced values", async () => {
    const user = userEvent.setup();
    render(<Form {...defaultProps} />);

    const nameInput = screen.getByLabelText(/name/i);

    await user.type(nameInput, "John");

    // Should not call immediately due to debouncing
    expect(mockUpdateFormField).not.toHaveBeenCalled();

    // Wait for debounce
    await waitFor(
      () => {
        expect(mockUpdateFormField).toHaveBeenCalledWith("name", "John");
      },
      { timeout: 1000 }
    );
  });

  it("displays voice input indicator when field is active", () => {
    const props = {
      ...defaultProps,
      activeVoiceField: "name" as const,
    };

    render(<Form {...props} />);

    expect(screen.getByText("Voice input")).toBeInTheDocument();
    expect(screen.getByLabelText(/name/i)).toHaveClass("ring-2", "ring-green-500");
  });

  it("displays updated indicator for last updated field", () => {
    const props = {
      ...defaultProps,
      lastUpdatedField: "email" as const,
    };

    render(<Form {...props} />);

    expect(screen.getByText("Updated")).toBeInTheDocument();
  });

  it("submits form when shouldSubmit prop is true", async () => {
    const props = {
      ...defaultProps,
      formData: {
        name: "John Doe",
        phone: "(*************",
        email: "<EMAIL>",
      },
      shouldSubmit: true,
    };

    render(<Form {...props} />);

    await waitFor(() => {
      expect(screen.getByText(/you're signed up for the ice cream party/i)).toBeInTheDocument();
      expect(mockConfetti).toHaveBeenCalled();
    });
  });
});
