import { Button } from "@/components/ui/button";
import { useEffect, useRef } from "react";

interface ConfirmationDialogProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  formData: {
    name: string;
    phone: string;
    email: string;
  };
}

export function ConfirmationDialog({
  isOpen,
  onConfirm,
  onCancel,
  formData,
}: ConfirmationDialogProps) {
  const dialogRef = useRef<HTMLDivElement>(null);
  const cancelButtonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (isOpen && cancelButtonRef.current) {
      cancelButtonRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onCancel();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen, onCancel]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onCancel}
        aria-hidden="true"
      />
      <div
        ref={dialogRef}
        className="relative w-full max-w-md p-4 sm:p-6 mx-4 bg-zinc-800 rounded-lg shadow-xl"
        role="dialog"
        aria-modal="true"
        aria-labelledby="dialog-title"
        aria-describedby="dialog-description"
      >
        <h2 id="dialog-title" className="text-lg sm:text-xl font-bold mb-3 sm:mb-4">
          Confirm Submission
        </h2>
        <div id="dialog-description" className="space-y-2 mb-4 sm:mb-6">
          <p className="text-sm text-gray-300">Please review your information before submitting:</p>
          <div className="space-y-2 p-3 sm:p-4 bg-zinc-700 rounded-md">
            <div className="flex justify-between items-start">
              <span className="text-sm text-gray-400 shrink-0">Name:</span>
              <span className="text-sm font-medium text-right break-all">{formData.name}</span>
            </div>
            <div className="flex justify-between items-start">
              <span className="text-sm text-gray-400 shrink-0">Phone:</span>
              <span className="text-sm font-medium text-right">{formData.phone}</span>
            </div>
            <div className="flex justify-between items-start">
              <span className="text-sm text-gray-400 shrink-0">Email:</span>
              <span className="text-sm font-medium text-right break-all">{formData.email}</span>
            </div>
          </div>
        </div>
        <div className="flex gap-2 sm:gap-3 justify-end flex-wrap">
          <Button
            ref={cancelButtonRef}
            variant="outline"
            onClick={onCancel}
            className="bg-zinc-700 hover:bg-zinc-600 border-zinc-600"
          >
            Cancel
          </Button>
          <Button onClick={onConfirm} className="bg-green-600 hover:bg-green-700">
            Confirm & Submit
          </Button>
        </div>
      </div>
    </div>
  );
}
