"use client";

import { logPerformance } from "@/lib/logger";
import { useEffect } from "react";

export function PerformanceMonitor() {
  useEffect(() => {
    // Log performance metrics when available
    if (typeof window !== "undefined" && window.performance) {
      // Wait for page load to complete
      if (document.readyState === "complete") {
        logMetrics();
      } else {
        window.addEventListener("load", logMetrics);
        return () => window.removeEventListener("load", logMetrics);
      }
    }

    function logMetrics() {
      const perfData = window.performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming;

      if (perfData) {
        // Core Web Vitals approximations
        const ttfb = perfData.responseStart - perfData.requestStart;
        const fcp = perfData.responseEnd - perfData.fetchStart;
        const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.fetchStart;
        const loadComplete = perfData.loadEventEnd - perfData.fetchStart;

        logPerformance("Time to First Byte (TTFB)", ttfb);
        logPerformance("First Contentful Paint (FCP)", fcp);
        logPerformance("DOM Content Loaded", domContentLoaded);
        logPerformance("Page Load Complete", loadComplete);
      }

      // Log resource timing
      const resources = window.performance.getEntriesByType("resource");
      const jsResources = resources.filter((r) => r.name.endsWith(".js"));
      const cssResources = resources.filter((r) => r.name.endsWith(".css"));

      if (jsResources.length > 0) {
        const totalJsTime = jsResources.reduce((acc, r) => acc + r.duration, 0);
        logPerformance(`JS Resources (${jsResources.length} files)`, totalJsTime);
      }

      if (cssResources.length > 0) {
        const totalCssTime = cssResources.reduce((acc, r) => acc + r.duration, 0);
        logPerformance(`CSS Resources (${cssResources.length} files)`, totalCssTime);
      }
    }
  }, []);

  return null;
}
