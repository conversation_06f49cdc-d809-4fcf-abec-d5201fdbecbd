#!/bin/bash

# Complete setup script for Apple Containerization Framework
# LiveKit Voice Agent Form Filler

set -e

echo "🍎 Apple Containerization Framework Setup"
echo "=========================================="
echo "Setting up LiveKit Voice Agent with Apple's revolutionary container technology"
echo ""

# Check macOS version
MACOS_VERSION=$(sw_vers -productVersion | cut -d. -f1)
echo "🖥️  macOS Version: $(sw_vers -productVersion)"

if [ "$MACOS_VERSION" -lt 26 ]; then
    echo "⚠️  Warning: Apple's containerization framework is optimized for macOS 26+"
    echo "   Some features may be limited on macOS $MACOS_VERSION"
    echo ""
fi

# Check if Apple Container CLI is installed
echo "🔍 Checking Apple Container CLI..."
if command -v container &> /dev/null; then
    echo "✅ Apple Container CLI found: $(container --version)"
else
    echo "❌ Apple Container CLI not found"
    echo "📦 Installing Apple Container CLI..."
    
    # Check if Homebrew is available
    if command -v brew &> /dev/null; then
        echo "Installing via Homebrew..."
        brew tap apple/containerization || true
        brew install container
    else
        echo "❌ Homebrew not found. Please install manually:"
        echo "   1. Download from Apple Developer Portal"
        echo "   2. Or install Homebrew first: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
fi

# Check if Swift is available
echo "🔍 Checking Swift..."
if command -v swift &> /dev/null; then
    echo "✅ Swift found: $(swift --version | head -n1)"
else
    echo "❌ Swift not found. Please install Xcode or Swift toolchain"
    exit 1
fi

# Check if Node.js is available
echo "🔍 Checking Node.js..."
if command -v node &> /dev/null; then
    echo "✅ Node.js found: $(node --version)"
else
    echo "❌ Node.js not found. Installing via Homebrew..."
    brew install node
fi

# Check if Python is available
echo "🔍 Checking Python..."
if command -v python3 &> /dev/null; then
    echo "✅ Python found: $(python3 --version)"
else
    echo "❌ Python not found. Installing via Homebrew..."
    brew install python
fi

echo ""
echo "🛠️  Setting up project dependencies..."

# Install Node.js dependencies
if [ -f "package.json" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
else
    echo "⚠️  package.json not found, skipping Node.js dependencies"
fi

# Install Python dependencies
if [ -f "agent/requirements.txt" ]; then
    echo "🐍 Installing Python dependencies..."
    cd agent
    pip3 install -r requirements.txt
    cd ..
else
    echo "⚠️  agent/requirements.txt not found, skipping Python dependencies"
fi

# Build Swift container manager
echo "🔨 Building Swift Container Manager..."
if [ -f "Package.swift" ]; then
    swift build
    echo "✅ Swift Container Manager built successfully"
else
    echo "⚠️  Package.swift not found, skipping Swift build"
fi

echo ""
echo "🔧 Setting up environment files..."

# Create environment files if they don't exist
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local from staging template..."
    cp .env.staging .env.local
    echo "⚠️  Please update .env.local with your actual API keys"
fi

if [ ! -f "agent/.env" ]; then
    echo "📝 Creating agent/.env from staging template..."
    cp agent/.env.staging agent/.env
    echo "⚠️  Please update agent/.env with your actual API keys"
fi

echo ""
echo "🚀 Starting Apple Container System..."
container system start

echo ""
echo "🏗️  Building container images..."

# Build frontend container
echo "📦 Building frontend container..."
container build -f Containerfile.frontend -t livekit-frontend:latest .

# Build agent container
echo "🤖 Building agent container..."
container build -f Containerfile.agent -t livekit-agent:latest .

echo ""
echo "✅ Setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Update environment files with your API keys:"
echo "   - .env.local (frontend configuration)"
echo "   - agent/.env (agent configuration)"
echo ""
echo "2. Start the development environment:"
echo "   ./scripts/dev-setup.sh"
echo ""
echo "3. Or start the production stack:"
echo "   ./apple-container-setup.sh"
echo ""
echo "4. Use the Swift Container Manager:"
echo "   swift run container-manager deploy"
echo "   swift run container-manager status"
echo ""
echo "📚 Documentation: APPLE_CONTAINERIZATION.md"
echo "🌍 Frontend will be available at: http://localhost:3000"
echo "📊 Monitoring at: http://localhost:3001 (after setup)"
echo ""
echo "🍎 Enjoy Apple's revolutionary containerization technology!"
