{"name": "voice-assistant2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format:check": "prettier --check .", "format:write": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@livekit/components-react": "^2.9.3", "@livekit/components-styles": "^1.1.4", "@radix-ui/react-slot": "^1.2.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.0", "livekit-client": "^2.8.0", "livekit-server-sdk": "^2.9.7", "lucide-react": "^0.510.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^29.5.14", "@types/node": "^20.17.13", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^8.57.1", "eslint-config-next": "14.2.28", "eslint-config-prettier": "9.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "next": "14", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}, "trustedDependencies": ["unrs-resolver"]}