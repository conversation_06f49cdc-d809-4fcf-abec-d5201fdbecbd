# Production Environment Configuration for Apple Containerization Framework
# LiveKit Voice Agent Form Filler - Production

# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-production.livekit.cloud
LIVEKIT_API_KEY=your_production_api_key
LIVEKIT_API_SECRET=your_production_api_secret

# Next.js Configuration
NODE_ENV=production
NEXT_PUBLIC_LIVEKIT_URL=wss://your-livekit-production.livekit.cloud
NEXT_PUBLIC_CONN_DETAILS_ENDPOINT=/api/connection-details
NEXT_PUBLIC_ENVIRONMENT=production

# Container Configuration
CONTAINER_ENVIRONMENT=production
CONTAINER_REGISTRY=ghcr.io
CONTAINER_NAMESPACE=livekit-prod

# Monitoring
HEALTH_CHECK_INTERVAL=30
LOG_LEVEL=info
ENABLE_METRICS=true

# Apple Container Framework Settings
APPLE_CONTAINER_ISOLATION=vm
APPLE_CONTAINER_SECURITY=enhanced
APPLE_CONTAINER_NETWORKING=dedicated

# Production Optimizations
APPLE_CONTAINER_PERFORMANCE_MODE=optimized
APPLE_CONTAINER_MEMORY_BALLOONING=enabled
APPLE_CONTAINER_CPU_PINNING=enabled

# Alerting
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
HEALTH_WEBHOOK_URL=https://your-monitoring-system.com/webhook
