#!/bin/bash

# Apple Containerization Framework Setup Script
# Requires macOS 26+ and Apple's container CLI

set -e

echo "🍎 Setting up LiveKit Voice Agent with Apple's Containerization Framework"

# Check if Apple's container CLI is installed
if ! command -v container &> /dev/null; then
    echo "❌ Apple container CLI not found. Please install from:"
    echo "   brew install apple/containerization/container"
    exit 1
fi

# Check macOS version
if [[ $(sw_vers -productVersion | cut -d. -f1) -lt 26 ]]; then
    echo "⚠️  Warning: Apple's containerization framework requires macOS 26+"
    echo "   Some features may be limited on earlier versions"
fi

# Start Apple's container system services
echo "🚀 Starting Apple container system..."
container system start

# Build frontend container
echo "📦 Building frontend container..."
container build -f Containerfile.frontend -t livekit-frontend:latest .

# Build agent container  
echo "🤖 Building agent container..."
container build -f Containerfile.agent -t livekit-agent:latest .

# Create network (Apple's framework provides dedicated IPs)
echo "🌐 Creating container network..."
container network create livekit-network || true

# Run frontend container
echo "🎨 Starting frontend container..."
container run -d \
  --name livekit-frontend \
  --network livekit-network \
  -p 3000:3000 \
  --env-file .env.local \
  livekit-frontend:latest

# Run agent container
echo "🎙️  Starting agent container..."
container run -d \
  --name livekit-agent \
  --network livekit-network \
  --env-file agent/.env \
  livekit-agent:latest

# Show running containers
echo "✅ Containers started successfully!"
echo ""
echo "📊 Container status:"
container ps

echo ""
echo "🌍 Frontend available at: http://localhost:3000"
echo "📝 View logs with: container logs <container-name>"
echo "🛑 Stop all with: container stop livekit-frontend livekit-agent"

# Optional: Show Apple's container performance metrics
echo ""
echo "📈 Performance metrics:"
container stats --no-stream
