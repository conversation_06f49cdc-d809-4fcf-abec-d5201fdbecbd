/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable image optimization
  images: {
    formats: ["image/avif", "image/webp"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Optimize production builds
  swcMinify: true,

  // Enable experimental optimizations
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ["@livekit/components-react", "framer-motion"],
  },
};

export default nextConfig;
